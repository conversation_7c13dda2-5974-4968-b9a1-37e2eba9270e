class VerificationEmailRequest {
  final String username;
  final String personName;

  VerificationEmailRequest({required this.username, required this.personName});

  Map<String, dynamic> toJson() {
    return {'username': username, 'personName': personName};
  }
}

class VerificationEmailResponse {
  final bool error;
  final String message;
  final String? responseData;

  VerificationEmailResponse({
    required this.error,
    required this.message,
    this.responseData,
  });

  factory VerificationEmailResponse.fromJson(Map<String, dynamic> json) {
    return VerificationEmailResponse(
      error: json['error'] ?? true,
      message: json['message'] ?? '',
      responseData: json['responseData'],
    );
  }

  bool get success => !error && responseData != null;
}
