import 'package:flutter/material.dart';
import '../widgets/app_header.dart';
import '../constants.dart';
import '../services/property_service.dart';
import '../models/property_models.dart';
import 'property_listings_view.dart';
import 'property_leases_view.dart';
import 'property_sales_view.dart';

class PropertyDetailScreen extends StatefulWidget {
  final String propertyId;
  final String loginEntityId;
  final String applicationId;

  const PropertyDetailScreen({
    super.key,
    required this.propertyId,
    required this.loginEntityId,
    required this.applicationId,
  });

  @override
  State<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends State<PropertyDetailScreen> {
  Future<void> _refreshDetails() async {
    await _loadPropertyDetails();
  }

  final ScrollController _scrollController = ScrollController();
  final PropertyService _propertyService = PropertyService();

  int _selectedTab = 0; // 0: Leases, 1: Sales, 2: Listings

  final List<PropertyLease> _leases = [
    PropertyLease(
      tenant: 'Intuit Australia',
      leasedSQM: '2,118',
      expiryDate: '25/4/2026',
    ),
    PropertyLease(
      tenant: 'Compass',
      leasedSQM: '1,211',
      expiryDate: '31/12/2027',
    ),
    PropertyLease(
      tenant: 'Alceon Group',
      leasedSQM: '745',
      expiryDate: '20/6/2029',
    ),
  ];

  final List<PropertySale> _sales = [
    PropertySale(
      buyer: 'Perpetual Trustee Company',
      price: '320,599,634',
      recordedDate: '25/4/2026',
    ),
    PropertySale(
      buyer: 'Harina Company',
      price: '178,100,000',
      recordedDate: '31/12/2020',
    ),
    PropertySale(
      buyer: 'Vynotas Pty Ltd',
      price: '158,100,000',
      recordedDate: '31/02/2012',
    ),
  ];
  PropertyDetail? _propertyDetail;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPropertyDetails();
  }

  Future<void> _loadPropertyDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _propertyService.getPropertyDetails(
        propertyId: widget.propertyId,
        loginEntityId: widget.loginEntityId,
        applicationId: widget.applicationId,
      );
      setState(() {
        _isLoading = false;
        if (response.success && response.data != null) {
          _propertyDetail = (response.data as PropertyDetailResponse).data;
        } else {
          _errorMessage = response.message;
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load property details';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppHeader(
        title: 'Arealitycs',
        showBackButton: true,
        actions: [
          IconButton(
            onPressed: null,
            icon: Icon(
              Icons.star_border,
              color: Colors.white,
              size: AppSizes.iconLG,
            ),
          ),
          IconButton(
            onPressed: null,
            icon: Icon(
              Icons.upload_rounded,
              color: Colors.white,
              size: AppSizes.iconLG,
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshDetails,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _errorMessage != null
            ? Center(child: Text('Error: $_errorMessage'))
            : _buildPropertyContent(),
      ),
    );
  }

  Widget _buildPropertyContent() {
    if (_propertyDetail == null) {
      return const Center(child: Text('No property data available'));
    }

    // Example listings data (replace with real data)
    final listings = [
      PropertyListing(
        totalAvailable: 3200,
        minDiv: 850,
        askingRate: '850 -  1,350',
        rateType: 'Net',
        listingType: 'Direct',
        agreementType: 'Exclusive',
        agentName: 'Oliver Archibald',
        agentCompany: 'Colliers',
  // ...existing code...

  // Tab button for main screen (must be at top-level, after all other classes)
  // ...existing code...

  // Tab button for main screen (must be at top-level, after all other classes)
  // ...existing code...
  // Tab button for main screen (must be at top-level, after all other classes)
    // ...existing code...
  
    // Tab button for main screen (must be at top-level, after all other classes)
    class _TabButton extends StatelessWidget {
      final String label;
      final bool selected;
      final VoidCallback onTap;
      const _TabButton({required this.label, required this.selected, required this.onTap});
  
      @override
      Widget build(BuildContext context) {
        return Expanded(
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: selected ? Colors.white : Colors.grey[200],
                border: Border(
                  bottom: BorderSide(
                    color: selected ? Colors.black : Colors.transparent,
                    width: 2,
                  ),
                ),
              ),
              child: Center(
                child: Text(
                  label,
                  style: TextStyle(
                    fontWeight: selected ? FontWeight.bold : FontWeight.normal,
                    color: selected ? Colors.black : Colors.grey,
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
          PropertySalesView(
            sales: _sales,
            onSaleTap: (sale) {
              // Handle sale tap
            },
          ),
        if (_selectedTab == 2)
          PropertyListingsView(
            listings: listings,
            onListingTap: (listing) {
              // Example navigation: you can customize this
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => PropertyDetailScreen(
                    propertyId: widget.propertyId,
                    loginEntityId: widget.loginEntityId,
                    applicationId: widget.applicationId,
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

// ...existing code...

// ...existing code...
// Tab button for main screen (must be at top-level, after all other classes)

class _TabButton extends StatelessWidget {
  final String label;
  final bool selected;
  final VoidCallback onTap;
  const _TabButton({required this.label, required this.selected, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: selected ? Colors.white : Colors.grey[200],
            border: Border(
              bottom: BorderSide(
                color: selected ? Colors.black : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontWeight: selected ? FontWeight.bold : FontWeight.normal,
                color: selected ? Colors.black : Colors.grey,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

  Widget _buildImageSection() {
    const imageUrl =
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ6qkdY-YkGBzF2RDbeuxjEQUU3uun7krrLXw&s';
    return Image.network(
      imageUrl,
      width: double.infinity,
      height: 300,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: double.infinity,
          height: 300,
          color: Colors.grey[300],
          child: const Icon(Icons.image_not_supported, size: 50),
        );
      },
    );
  }

  Widget _buildPropertyInfo() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Contact Agent',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'For sale',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
          ),
          Text(
            _propertyDetail?.propertyName ?? 'No name',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.normal),
          ),
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _InfoColumn(
                icon: Icons.square_foot,
                label: 'Property Type',
                value: _propertyDetail?.propertyUse ?? 'N/A',
              ),
              _InfoColumn(
                icon: Icons.layers,
                label: 'Building Size(GBA)',
                value: _propertyDetail?.constructionStatusName ?? 'N/A',
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _InfoColumn(
                icon: Icons.assignment_ind,
                label: 'Structure Type',
                value: _propertyDetail?.condoTypeName ?? 'N/A',
              ),
              _InfoColumn(
                icon: Icons.directions_car,
                label: 'Land Size',
                value: _propertyDetail?.lotSizeSM ?? 'N/A',
              ),
            ],
          ),
          // const SizedBox(height: 16),
          // _InfoColumn(
          //   icon: Icons.person,
          //   label: 'Contact',
          //   value:
          //       '${_propertyDetail?.personFirstName ?? ''} ${_propertyDetail?.personLastName ?? ''}',
          // ),
          // const SizedBox(height: 16),
          // _InfoColumn(
          //   icon: Icons.apartment,
          //   label: 'Construction Type',
          //   value: _propertyDetail?.constructionTypeName ?? 'N/A',
          // ),
          // const SizedBox(height: 16),
          // _InfoColumn(
          //   icon: Icons.info_outline,
          //   label: 'Size Source',
          //   value: _propertyDetail?.sizeSourceName ?? 'N/A',
          // ),
        ],
      ),
    );
  }
}

class _InfoColumn extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoColumn({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, size: 20),
        SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(label, style: TextStyle(fontSize: 12, color: Colors.grey)),
            Text(value, style: TextStyle(fontSize: 14)),
          ],
        ),
      ],
    );
  }
}
