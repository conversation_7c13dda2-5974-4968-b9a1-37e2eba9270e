import 'package:flutter/material.dart';
import '../widgets/app_header.dart';
import '../constants.dart';
import '../services/property_service.dart';
import '../models/property_models.dart';
import '../utils.dart';

class PropertyDetailScreen extends StatefulWidget {
  final String propertyId;
  final String loginEntityId;
  final String applicationId;

  const PropertyDetailScreen({
    super.key,
    required this.propertyId,
    required this.loginEntityId,
    required this.applicationId,
  });

  @override
  State<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends State<PropertyDetailScreen> {
  PropertyDetail? _propertyDetail;
  bool _isLoading = true;
  String? _errorMessage;

  final ScrollController _scrollController = ScrollController();
  final PropertyService _propertyService = PropertyService();

  @override
  void initState() {
    super.initState();
    _loadPropertyDetails();
  }

  Future<void> _refreshDetails() async {
    await _loadPropertyDetails();
  }

  Future<void> _loadPropertyDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _propertyService.getPropertyDetails(
        propertyId: widget.propertyId,
        loginEntityId: widget.loginEntityId,
        applicationId: widget.applicationId,
      );
      setState(() {
        _isLoading = false;
        if (response.success && response.data != null) {
          _propertyDetail = (response.data as PropertyDetailResponse).data;
        } else {
          _errorMessage = response.message;
          AppUtils.showErrorToast(_errorMessage ?? 'Error occurred');
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load property details';
        AppUtils.showErrorToast(_errorMessage ?? 'Error occurred');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppHeader(
        title: 'Arealitycs',
        showBackButton: true,
        actions: [
          IconButton(
            onPressed: null,
            icon: Icon(
              Icons.star_border,
              color: Colors.white,
              size: AppSizes.iconLG,
            ),
          ),
          IconButton(
            onPressed: null,
            icon: Icon(
              Icons.upload_rounded,
              color: Colors.white,
              size: AppSizes.iconLG,
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshDetails,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _errorMessage != null
            ? Center(child: Text('No Property Details Found'))
            : _buildPropertyContent(),
      ),
    );
  }

  Widget _buildPropertyContent() {
    if (_propertyDetail == null) {
      return const Center(child: Text('No property data available'));
    }

    return ListView(
      controller: _scrollController,
      padding: EdgeInsets.zero,
      physics: const AlwaysScrollableScrollPhysics(),
      children: [_buildImageSection(), _buildPropertyInfo()],
    );
  }

  Widget _buildImageSection() {
    final imageUrl =
        'https://media.empiricalcre.com/EmpiricalCRE/DEV/Media/Thumbnail/300x300/${_propertyDetail?.mainPhotoUrl ?? ""}';
    return Image.network(
      imageUrl,
      width: double.infinity,
      height: 300,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: double.infinity,
          height: 300,
          color: Colors.grey[300],
          child: const Icon(Icons.image_not_supported, size: 50),
        );
      },
    );
  }

  Widget _buildPropertyInfo() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${_propertyDetail?.address ?? ''}\n${_propertyDetail?.cityName ?? ''} ${_propertyDetail?.stateAbbr ?? ''} ${_propertyDetail?.zipCode ?? ''}',
            style: TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _InfoColumn(
                icon: Icons.square_foot,
                label: 'Property Type',
                value: _propertyDetail?.propertyUse ?? 'N/A',
              ),
              _InfoColumn(
                icon: Icons.layers,
                label: 'Building Size sqm',
                value: _propertyDetail?.constructionStatusName ?? 'N/A',
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _InfoColumn(
                icon: Icons.assignment_ind,
                label: 'Structure Type',
                value: _propertyDetail?.condoTypeName ?? 'N/A',
              ),
              _InfoColumn(
                icon: Icons.directions_car,
                label: 'Land Size sqm',
                value: _propertyDetail?.lotSizeSM ?? 'N/A',
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _InfoColumn extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoColumn({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, size: 20),
        SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(label, style: TextStyle(fontSize: 14, color: Colors.grey)),
            Text(value, style: TextStyle(fontSize: 16)),
          ],
        ),
      ],
    );
  }
}
