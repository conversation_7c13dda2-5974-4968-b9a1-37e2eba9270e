class PropertyDetailResponse {
  final bool success;
  final PropertyDetail? data;
  final String message;

  PropertyDetailResponse({
    required this.success,
    this.data,
    required this.message,
  });

  factory PropertyDetailResponse.fromJson(Map<String, dynamic> json) {
    return PropertyDetailResponse(
      success: !(json['error'] ?? true),
      data:
          (json['responseData'] != null &&
              (json['responseData'] as List).isNotEmpty)
          ? PropertyDetail.fromJson(json['responseData'][0])
          : null,
      message: json['message'] ?? '',
    );
  }
}

class PropertyDetail {
  final int? propertyId;
  final String? propertyName;
  final int? floors;
  final String? constructionStatusName;
  final String? propertyUse;
  final String? personFirstName;
  final String? personLastName;
  final String? constructionTypeName;
  final String? tenancyName;
  final String? sizeSourceName;
  final String? classTypeName;
  final String? metroName;
  final String? marketName;
  final String? address;
  final String? cityName;
  final String? stateName;
  final String? zipCode;
  final String? countryName;
  final String? mainPhotoUrl;
  final double? latitude;
  final double? longitude;
  final String? propertyManagerName;
  final String? propertyManagerPhone;
  final String? propertyManagerEmail;
  final String? propertyManagerWebsite;
  final String? propertyManagerAddress;
  final String? condoTypeName;
  final String? condoUnit;
  final String? subMarket;
  final String? propertyComments;
  final String? buildingSizeSMFormatted;
  final double? lotSizeAC;
  final String? lotSizeACSM;
  final String? lotSizeSM;
  final String? officeSM;
  final String? officeSF;
  final String? yearBuilt;
  final String? yearRenovated;
  final String? propertyCreatedDate;
  final String? propertyModifiedDate;
  final String? propertyManagerAddress1;
  final String? propertyManagerAddress2;
  final String? propertyManagerCity;
  final String? propertyManagerState;
  final String? propertyManagerZip;
  final String? propertyManagerCountry;

  PropertyDetail({
    this.propertyId,
    this.propertyName,
    this.floors,
    this.constructionStatusName,
    this.propertyUse,
    this.personFirstName,
    this.personLastName,
    this.constructionTypeName,
    this.tenancyName,
    this.sizeSourceName,
    this.classTypeName,
    this.metroName,
    this.marketName,
    this.address,
    this.cityName,
    this.stateName,
    this.zipCode,
    this.countryName,
    this.mainPhotoUrl,
    this.latitude,
    this.longitude,
    this.propertyManagerName,
    this.propertyManagerPhone,
    this.propertyManagerEmail,
    this.propertyManagerWebsite,
    this.propertyManagerAddress,
    this.condoTypeName,
    this.condoUnit,
    this.subMarket,
    this.propertyComments,
    this.buildingSizeSMFormatted,
    this.lotSizeAC,
    this.lotSizeACSM,
    this.lotSizeSM,
    this.officeSM,
    this.officeSF,
    this.yearBuilt,
    this.yearRenovated,
    this.propertyCreatedDate,
    this.propertyModifiedDate,
    this.propertyManagerAddress1,
    this.propertyManagerAddress2,
    this.propertyManagerCity,
    this.propertyManagerState,
    this.propertyManagerZip,
    this.propertyManagerCountry,
  });

  factory PropertyDetail.fromJson(Map<String, dynamic> json) {
    return PropertyDetail(
      propertyId: json['PropertyID'],
      propertyName: json['PropertyName'],
      floors: json['Floors'],
      constructionStatusName: json['ConstructionStatusName'],
      propertyUse: json['PropertyUse'],
      personFirstName: json['PersonFirstName'],
      personLastName: json['PersonLastName'],
      constructionTypeName: json['ConstructionTypeName'],
      tenancyName: json['TenancyName'],
      sizeSourceName: json['SizeSourceName'],
      classTypeName: json['ClassTypeName'],
      metroName: json['MetroName'],
      marketName: json['MarketName'],
      address: json['Address'],
      cityName: json['CityName'],
      stateName: json['StateName'],
      zipCode: json['ZipCode'],
      countryName: json['CountryName'],
      mainPhotoUrl: json['MainPhotoUrl'],
      latitude: (json['Latitude'] != null)
          ? double.tryParse(json['Latitude'].toString())
          : null,
      longitude: (json['Longitude'] != null)
          ? double.tryParse(json['Longitude'].toString())
          : null,
      propertyManagerName: json['PropertyManagerName'],
      propertyManagerPhone: json['PropertyManagerPhone'],
      propertyManagerEmail: json['PropertyManagerEmail'],
      propertyManagerWebsite: json['PropertyManagerWebsite'],
      propertyManagerAddress: json['PropertyManagerAddress'],
      condoTypeName: json['CondoTypeName'],
      condoUnit: json['CondoUnit'],
      subMarket: json['SubMarket'],
      propertyComments: json['PropertyComments'],
      buildingSizeSMFormatted: json['BuildingSizeSMFormatted'],
      lotSizeAC: (json['LotSizeAC'] != null)
          ? double.tryParse(json['LotSizeAC'].toString())
          : null,
      lotSizeACSM: json['LotSizeACSM'],
      lotSizeSM: json['LotSizeSM'],
      officeSM: json['OfficeSM'],
      officeSF: json['OfficeSF'],
      yearBuilt: json['YearBuilt']?.toString(),
      yearRenovated: json['YearRenovated']?.toString(),
      propertyCreatedDate: json['PropertyCreatedDate'],
      propertyModifiedDate: json['PropertyModifiedDate'],
      propertyManagerAddress1: json['PropertyManagerAddress1'],
      propertyManagerAddress2: json['PropertyManagerAddress2'],
      propertyManagerCity: json['PropertyManagerCity'],
      propertyManagerState: json['PropertyManagerState'],
      propertyManagerZip: json['PropertyManagerZip'],
      propertyManagerCountry: json['PropertyManagerCountry'],
    );
  }

  String get fullAddress => address ?? propertyName ?? '';
}
