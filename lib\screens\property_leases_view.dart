import 'package:flutter/material.dart';

class PropertyLeasesView extends StatelessWidget {
  final List<PropertyLease> leases;
  final void Function(PropertyLease) onLeaseTap;

  const PropertyLeasesView({
    super.key,
    required this.leases,
    required this.onLeaseTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab bar for Leases/Sales/Listings
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              _TabButton(label: 'Leases', selected: true),
              _TabButton(label: 'Sales', selected: false),
              _TabButton(label: 'Listings', selected: false),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Lease details
        _LeaseDetailsHeader(),
        ...leases.map(
          (lease) => _LeaseCard(lease: lease, onTap: () => onLeaseTap(lease)),
        ),
      ],
    );
  }
}

class _TabButton extends StatelessWidget {
  final String label;
  final bool selected;
  const _TabButton({required this.label, required this.selected});
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: selected ? Colors.white : Colors.grey[200],
          border: Border(
            bottom: BorderSide(
              color: selected ? Colors.black : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              fontWeight: selected ? FontWeight.bold : FontWeight.normal,
              color: selected ? Colors.black : Colors.grey,
            ),
          ),
        ),
      ),
    );
  }
}

class _LeaseDetailsHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                Text(
                  'YPOL Lawyers',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Tenant', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('1,350', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Leased SQM', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('1900', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(
                  'Starting Rent/SQM/Yr',
                  style: TextStyle(color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Dec 15, 2024',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Commencement Date', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('Original', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Transaction Type', style: TextStyle(color: Colors.grey)),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                Text('Level 4', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Floor / Suite', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('36', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Term (months)', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('Net', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Lease Rate Type', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text(
                  'Dec 14, 2027',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Expiry Date', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text(
                  '3.75% Fixed',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Escalation Type', style: TextStyle(color: Colors.grey)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _LeaseCard extends StatelessWidget {
  final PropertyLease lease;
  final VoidCallback onTap;
  const _LeaseCard({required this.lease, required this.onTap});
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tenant: ${lease.tenant}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                'Leased SQM: ${lease.leasedSQM}',
                style: const TextStyle(fontWeight: FontWeight.normal),
              ),
              Text(
                'Expiry: ${lease.expiryDate}',
                style: const TextStyle(fontWeight: FontWeight.normal),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  icon: const Icon(Icons.add_circle, size: 28),
                  onPressed: onTap,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PropertyLease {
  final String tenant;
  final String leasedSQM;
  final String expiryDate;

  PropertyLease({
    required this.tenant,
    required this.leasedSQM,
    required this.expiryDate,
  });
}
