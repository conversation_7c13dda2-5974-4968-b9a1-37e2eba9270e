class ValidateResetCodeRequest {
  final String username;
  final String userResetCode;
  final String hashedResetCode;

  ValidateResetCodeRequest({
    required this.username,
    required this.userResetCode,
    required this.hashedResetCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'userResetCode': userResetCode,
      'hashedResetCode': hashedResetCode,
    };
  }
}

class ValidateResetCodeResponse {
  final bool error;
  final String message;
  final dynamic responseData;

  ValidateResetCodeResponse({
    required this.error,
    required this.message,
    this.responseData,
  });

  factory ValidateResetCodeResponse.fromJson(Map<String, dynamic> json) {
    return ValidateResetCodeResponse(
      error: json['error'] ?? true,
      message: json['message'] ?? '',
      responseData: json['responseData'],
    );
  }

  bool get success => !error;
}
