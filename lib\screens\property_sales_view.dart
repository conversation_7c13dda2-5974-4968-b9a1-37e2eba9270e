import 'package:flutter/material.dart';

class PropertySalesView extends StatelessWidget {
  final List<PropertySale> sales;
  final void Function(PropertySale) onSaleTap;

  const PropertySalesView({
    super.key,
    required this.sales,
    required this.onSaleTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab bar for Sales/Leases/Listings
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              _TabButton(label: 'Sales', selected: true),
              _TabButton(label: 'Leases', selected: false),
              _TabButton(label: 'Listings', selected: false),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Sale details
        _SaleDetailsHeader(),
        ...sales.map(
          (sale) => _SaleCard(sale: sale, onTap: () => onSaleTap(sale)),
        ),
      ],
    );
  }
}

class _TabButton extends StatelessWidget {
  final String label;
  final bool selected;
  const _TabButton({required this.label, required this.selected});
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: selected ? Colors.white : Colors.grey[200],
          border: Border(
            bottom: BorderSide(
              color: selected ? Colors.black : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              fontWeight: selected ? FontWeight.bold : FontWeight.normal,
              color: selected ? Colors.black : Colors.grey,
            ),
          ),
        ),
      ),
    );
  }
}

class _SaleDetailsHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                Text(
                  'LendLease RE Inv.',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Buyer', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text(
                  '334,500,000',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Sale Price', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('57,150', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Sold SQM', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text(
                  'Dec 15, 2024',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Recorded Date', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text(
                  'Investment',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Sale Type', style: TextStyle(color: Colors.grey)),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                Text(
                  'Harina Company',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Seller', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('5,832', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Price/SQM', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('3,211', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Land Size SQM', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text('Freehold', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('Property Rights', style: TextStyle(color: Colors.grey)),
                SizedBox(height: 8),
                Text(
                  'Land & Building',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Conveyed', style: TextStyle(color: Colors.grey)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SaleCard extends StatelessWidget {
  final PropertySale sale;
  final VoidCallback onTap;
  const _SaleCard({required this.sale, required this.onTap});
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Buyer: ${sale.buyer}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                'Price: ${sale.price}',
                style: const TextStyle(fontWeight: FontWeight.normal),
              ),
              Text(
                'Recorded Date: ${sale.recordedDate}',
                style: const TextStyle(fontWeight: FontWeight.normal),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  icon: const Icon(Icons.add_circle, size: 28),
                  onPressed: onTap,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PropertySale {
  final String buyer;
  final String price;
  final String recordedDate;

  PropertySale({
    required this.buyer,
    required this.price,
    required this.recordedDate,
  });
}
