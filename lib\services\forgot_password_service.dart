import '../models/forgot_password/index.dart';
import '../services/api_service.dart';
import '../utils.dart';

class ForgotPasswordService {
  final ApiService _apiService = ApiService();

  Future<ApiResponse<VerificationEmailResponse>> sendVerificationEmail({
    required String username,
  }) async {
    try {
      final request = VerificationEmailRequest(
        username: username,
        personName:
            username, // Using username as personName as per your example
      );

      final response = await _apiService.post(
        'verificationemail',
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final verificationResponse = VerificationEmailResponse.fromJson(
          response.data,
        );
        return ApiResponse<VerificationEmailResponse>(
          success: verificationResponse.success,
          data: verificationResponse,
          message: verificationResponse.message,
          statusCode: 200,
        );
      } else {
        return ApiResponse<VerificationEmailResponse>(
          success: false,
          message: response.message,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse<VerificationEmailResponse>(
        success: false,
        message: AppUtils.getErrorMessage(e),
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<ValidateResetCodeResponse>> validateResetCode({
    required String username,
    required String userResetCode,
    required String hashedResetCode,
  }) async {
    try {
      final request = ValidateResetCodeRequest(
        username: username,
        userResetCode: userResetCode,
        hashedResetCode: hashedResetCode,
      );

      final response = await _apiService.post(
        'validateResetCode',
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final validateResponse = ValidateResetCodeResponse.fromJson(
          response.data,
        );
        return ApiResponse<ValidateResetCodeResponse>(
          success: validateResponse.success,
          data: validateResponse,
          message: validateResponse.message,
          statusCode: 200,
        );
      } else {
        return ApiResponse<ValidateResetCodeResponse>(
          success: false,
          message: response.message,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse<ValidateResetCodeResponse>(
        success: false,
        message: AppUtils.getErrorMessage(e),
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<ResetPasswordResponse>> resetPassword({
    required String username,
    required String password,
  }) async {
    try {
      final request = ResetPasswordRequest(
        username: username,
        password: password,
      );

      final response = await _apiService.post(
        'resetPassword',
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final resetResponse = ResetPasswordResponse.fromJson(response.data);
        return ApiResponse<ResetPasswordResponse>(
          success: resetResponse.success,
          data: resetResponse,
          message: resetResponse.message,
          statusCode: 200,
        );
      } else {
        return ApiResponse<ResetPasswordResponse>(
          success: false,
          message: response.message,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse<ResetPasswordResponse>(
        success: false,
        message: AppUtils.getErrorMessage(e),
        statusCode: 500,
      );
    }
  }

  // Password strength validation
  static PasswordStrengthResult checkPasswordStrength(String password) {
    if (password.isEmpty) {
      return PasswordStrengthResult(
        strength: PasswordStrength.weak,
        message: 'Password is required',
        score: 0.0,
      );
    }

    double score = 0.0;
    List<String> issues = [];

    // Length check (minimum 8 characters)
    if (password.length >= 8) {
      score += 0.25;
    } else {
      issues.add('at least 8 characters');
    }

    // Uppercase letter check
    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 0.25;
    } else {
      issues.add('one uppercase letter');
    }

    // Lowercase letter check
    if (password.contains(RegExp(r'[a-z]'))) {
      score += 0.25;
    } else {
      issues.add('one lowercase letter');
    }

    // Number check
    if (password.contains(RegExp(r'[0-9]'))) {
      score += 0.25;
    } else {
      issues.add('one number');
    }

    // Determine strength and message
    PasswordStrength strength;
    String message;

    if (score >= 1.0) {
      strength = PasswordStrength.strong;
      message = 'Strong password';
    } else if (score >= 0.5) {
      strength = PasswordStrength.medium;
      message = 'Medium strength - Missing: ${issues.join(', ')}';
    } else {
      strength = PasswordStrength.weak;
      message = 'Weak password - Need: ${issues.join(', ')}';
    }

    return PasswordStrengthResult(
      strength: strength,
      message: message,
      score: score,
    );
  }

  // Validate password meets all requirements
  static bool isPasswordValid(String password) {
    final result = checkPasswordStrength(password);
    return result.score >= 1.0;
  }
}
