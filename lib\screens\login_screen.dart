import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:retail_app/constants.dart';
import 'package:retail_app/widgets/bottom_navigation.dart';
import '../services/auth_service.dart';
import '../services/remember_email_service.dart';
import '../utils.dart';
import '../utils/snackbar_utils.dart';
import '../routes.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isRememberMe = false;
  bool _isUsernameValidated = false;
  bool _isLoading = false;
  bool _isValidatingUsername = false;
  bool _showEmailDropdown = false;

  List<String> _rememberedEmails = [];

  final AuthService _authService = AuthService();
  final RememberEmailService _rememberEmailService = RememberEmailService();

  @override
  void initState() {
    super.initState();
    _setSystemUIOverlayStyle();
    _loadRememberedEmails();
    _loadLastEmail();
  }

  void _setSystemUIOverlayStyle() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.loginPrimaryColor,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loadRememberedEmails() async {
    final emails = await _rememberEmailService.getRememberedEmails();
    setState(() {
      _rememberedEmails = emails;
    });
  }

  Future<void> _loadLastEmail() async {
    final lastEmail = await _rememberEmailService.getLastRememberedEmail();
    if (lastEmail != null) {
      setState(() {
        _emailController.text = lastEmail;
        _isRememberMe = true;
      });
    }
  }

  Future<void> _validateUsername() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isValidatingUsername = true;
    });

    try {
      final response = await _authService.validateUsername(
        _emailController.text.trim(),
      );

      if (response.success && response.data != null) {
        final isRegistered = response.data!.isRegistered;
        setState(() {
          _isUsernameValidated = isRegistered;
        });

        if (!isRegistered && mounted) {
          SnackBarUtils.showError(
            context,
            AppStrings.userNotFound,
          );
        }
      } else {
        // Show error message from API response or generic message
        String errorMessage = AppStrings.usernameValidationFailed;
        if (response.data?.responseData.loginStatus.isNotEmpty == true) {
          // Check if there's an error from the login status
          errorMessage =
              AppStrings.userValidationFailed;
        }
        if (mounted) {
          SnackBarUtils.showError(context, errorMessage);
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(
          context,
          AppStrings.usernameValidationRetry,
        );
      }
    } finally {
      setState(() {
        _isValidatingUsername = false;
      });
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _authService.login(
        username: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (response.success && response.data != null) {
        // Remember email if checkbox is checked
        if (_isRememberMe) {
          await _rememberEmailService.addRememberedEmail(
            _emailController.text.trim(),
          );
        }

        if (mounted) {
          SnackBarUtils.showSuccess(context, AppConstants.loginSuccessMessage);
        }

        // Navigate to home screen
        if (mounted) {
          Navigator.of(context).pushReplacement(
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const MainBottomNavigation(),
              transitionDuration: const Duration(milliseconds: 500),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                    return FadeTransition(opacity: animation, child: child);
                  },
            ),
          );
        }
      } else {
        debugPrint('coming here after failing login: $response');
        if (mounted) {
          SnackBarUtils.showError(context, response.message);
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(context, AppStrings.loginFailed);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onSelectRememberedEmail(String email) {
    setState(() {
      _emailController.text = email;
      _showEmailDropdown = false;
      _isUsernameValidated = false;
    });
  }

  Widget _buildEmailItem(String email, int index) {
    return InkWell(
      onTap: () => _onSelectRememberedEmail(email),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.borderColor,
              width: index < _rememberedEmails.length - 1 ? 1 : 0,
            ),
          ),
        ),
        child: Text(
          email,
          style: const TextStyle(fontSize: 16, color: AppColors.textPrimaryColor),
        ),
      ),
    );
  }

  void _toggleEmailDropdown() {
    setState(() {
      _showEmailDropdown = !_showEmailDropdown;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            // Top purple section with content
            SizedBox(
              height:
                  MediaQuery.of(context).size.height * 0.4 +
                  MediaQuery.of(context).padding.top,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/login_background.png'),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    // color: Color(0xFF2567E8), // Blue overlay color
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColors.loginPrimaryColor.withValues(alpha: 0.8),
                          AppColors.loginPrimaryColor.withValues(alpha: 0.9),
                        ],
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                        32.0,
                        MediaQuery.of(context).padding.top + 32.0,
                        32.0,
                        32.0,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Logo/Shield icon
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.security,
                              size: 40,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 40),

                          // Title
                          const Text(
                            AppStrings.signInToArealytics,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 32,
                              fontWeight: FontWeight.w700,
                              height: 1.2,
                              fontFamily: AppStrings.defaultFontFamily,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Account text in yellow
                          const Text(
                            AppStrings.accountText,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 32,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 24),

                          // Subtitle
                          const Text(
                            AppStrings.loginSubtitle,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppStrings.defaultFontFamily,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Bottom white section with form
            Expanded(
              flex: 4,
              child: Container(
                width: double.infinity,
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32.0,
                    vertical: 32,
                  ),
                  child: GestureDetector(
                    onTap: () {
                      if (_showEmailDropdown) {
                        setState(() {
                          _showEmailDropdown = false;
                        });
                      }
                    },
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          // Email field with dropdown
                          Stack(
                            clipBehavior: Clip.none,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: AppColors.inputBackgroundColor,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: TextFormField(
                                  controller: _emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  decoration: InputDecoration(
                                    hintText: AppStrings.emailPlaceholder,
                                    hintStyle: const TextStyle(
                                      color: AppColors.inputHintColor,
                                      fontSize: 16,
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 16,
                                    ),
                                    suffixIcon: _rememberedEmails.isNotEmpty
                                        ? IconButton(
                                            icon: Icon(
                                              _showEmailDropdown
                                                  ? Icons.keyboard_arrow_up
                                                  : Icons.keyboard_arrow_down,
                                              color: AppColors.inputHintColor,
                                            ),
                                            onPressed: _toggleEmailDropdown,
                                          )
                                        : null,
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return AppStrings.emailRequired;
                                    }
                                    if (!AppUtils.isValidEmail(value)) {
                                      return AppStrings.validEmailRequired;
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    if (_isUsernameValidated) {
                                      setState(() {
                                        _isUsernameValidated = false;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),

                          // Email dropdown overlay
                          if (_showEmailDropdown &&
                              _rememberedEmails.isNotEmpty)
                            Container(
                              margin: const EdgeInsets.only(top: 2),
                              child: Material(
                                elevation: 16,
                                borderRadius: BorderRadius.circular(8),
                                shadowColor: Colors.black.withValues(
                                  alpha: 0.3,
                                ),
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxHeight: _rememberedEmails.length > 3
                                        ? 180.0
                                        : double.infinity,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: AppColors.borderColor,
                                    ),
                                  ),
                                  child: _rememberedEmails.length > 3
                                      ? Scrollbar(
                                          child: ListView.builder(
                                            shrinkWrap: true,
                                            itemCount: _rememberedEmails.length,
                                            itemBuilder: (context, index) {
                                              final email =
                                                  _rememberedEmails[index];
                                              return _buildEmailItem(
                                                email,
                                                index,
                                              );
                                            },
                                          ),
                                        )
                                      : Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: _rememberedEmails
                                              .asMap()
                                              .entries
                                              .map(
                                                (entry) => _buildEmailItem(
                                                  entry.value,
                                                  entry.key,
                                                ),
                                              )
                                              .toList(),
                                        ),
                                ),
                              ),
                            ),

                          const SizedBox(height: 20),

                          // Password field (only show if username is validated)
                          if (_isUsernameValidated)
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.inputBackgroundColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: TextFormField(
                                controller: _passwordController,
                                obscureText: !_isPasswordVisible,
                                decoration: InputDecoration(
                                  hintText: AppStrings.passwordPlaceholder,
                                  hintStyle: const TextStyle(
                                    color: AppColors.inputHintColor,
                                    fontSize: 16,
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 16,
                                  ),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      _isPasswordVisible
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: AppColors.inputHintColor,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _isPasswordVisible =
                                            !_isPasswordVisible;
                                      });
                                    },
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return AppStrings.passwordRequired;
                                  }
                                  return null;
                                },
                              ),
                            ),

                          if (_isUsernameValidated) const SizedBox(height: 20),

                          // Remember me checkbox and Forgot password
                          if (_isUsernameValidated)
                            Row(
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: Checkbox(
                                    value: _isRememberMe,
                                    onChanged: (value) {
                                      setState(() {
                                        _isRememberMe = value ?? false;
                                      });
                                    },
                                    activeColor: AppColors.loginButtonColor,
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    visualDensity: VisualDensity.compact,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  AppStrings.rememberMe,
                                  style: TextStyle(
                                    color: AppColors.loginButtonColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const Spacer(),
                                TextButton(
                                  onPressed: () {
                                    NavigationHelper.toForgotPassword(context);
                                  },
                                  child: const Text(
                                    AppStrings.forgotPassword,
                                    style: TextStyle(
                                      color: AppColors.loginButtonColor,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                          const SizedBox(height: 32),

                          // Login/Continue button
                          SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              onPressed: (_isLoading || _isValidatingUsername)
                                  ? null
                                  : _isUsernameValidated
                                  ? _login
                                  : _validateUsername,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.loginButtonColor,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                disabledBackgroundColor:
                                    AppColors.primaryLightColor,
                              ),
                              child: _isLoading || _isValidatingUsername
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : Text(
                                      _isUsernameValidated
                                          ? AppStrings.loginButton
                                          : AppStrings.continueButton,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: AppStrings.defaultFontFamily,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
