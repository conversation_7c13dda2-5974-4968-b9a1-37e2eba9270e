import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'constants.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:async';

class AppUtils {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // SnackBar Messages - Deprecated: Use SnackBarUtils directly with context
  @Deprecated('Use SnackBarUtils.showSuccess(context, message) instead')
  static void showSuccessToast(String message) {
    final context = _currentContext;
    if (context != null) {
      _showSnackBar(message, AppColors.successColor);
    }
  }

  @Deprecated('Use SnackBarUtils.showError(context, message) instead')
  static void showErrorToast(String message) {
    final context = _currentContext;
    if (context != null) {
      _showSnackBar(message, AppColors.errorColor);
    }
  }

  @Deprecated('Use SnackBarUtils.showInfo(context, message) instead')
  static void showInfoToast(String message) {
    final context = _currentContext;
    if (context != null) {
      _showSnackBar(message, AppColors.infoColor);
    }
  }

  @Deprecated('Use SnackBarUtils.showWarning(context, message) instead')
  static void showWarningToast(String message) {
    final context = _currentContext;
    if (context != null) {
      _showSnackBar(message, AppColors.warningColor);
    }
  }

  static void _showSnackBar(String message, Color backgroundColor) {
    final context = _currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message, style: const TextStyle(color: Colors.white)),
          backgroundColor: backgroundColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusSM),
          ),
          margin: const EdgeInsets.all(AppSizes.paddingMD),
        ),
      );
    }
  }

  // Global context for showing snackbars - Deprecated
  @Deprecated('Use SnackBarUtils with direct context instead')
  static BuildContext? _currentContext;

  @Deprecated('Use SnackBarUtils with direct context instead')
  static void setContext(BuildContext context) {
    _currentContext = context;
  }

  // Connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }

      // Additional check by trying to reach a reliable server
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Secure Storage
  static Future<void> saveSecureData(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      debugPrint('Error saving secure data: $e');
    }
  }

  static Future<String?> getSecureData(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      debugPrint('Error reading secure data: $e');
      return null;
    }
  }

  static Future<void> deleteSecureData(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      debugPrint('Error deleting secure data: $e');
    }
  }

  static Future<void> clearAllSecureData() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      debugPrint('Error clearing all secure data: $e');
    }
  }

  // Token Management
  static Future<void> saveAccessToken(String token) async {
    await saveSecureData(AppConstants.accessTokenKey, token);
  }

  static Future<String?> getAccessToken() async {
    return await getSecureData(AppConstants.accessTokenKey);
  }

  static Future<void> saveRefreshToken(String token) async {
    await saveSecureData(AppConstants.refreshTokenKey, token);
  }

  static Future<String?> getRefreshToken() async {
    return await getSecureData(AppConstants.refreshTokenKey);
  }

  static Future<void> clearTokens() async {
    await deleteSecureData(AppConstants.accessTokenKey);
    await deleteSecureData(AppConstants.refreshTokenKey);
    await deleteSecureData(AppConstants.userIdKey);
    await deleteSecureData(AppConstants.userDataKey);
    await deleteSecureData(AppConstants.apiTokenKey);
    await deleteSecureData(AppConstants.loginUserInfoKey);
  }

  // User Data Management
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    final jsonString = jsonEncode(userData);
    await saveSecureData(AppConstants.userDataKey, jsonString);
  }

  static Future<Map<String, dynamic>?> getUserData() async {
    final jsonString = await getSecureData(AppConstants.userDataKey);
    if (jsonString != null) {
      return jsonDecode(jsonString);
    }
    return null;
  }

  // API Token Management - Store Token from login response
  static Future<void> saveApiToken(String token) async {
    await saveSecureData(AppConstants.apiTokenKey, token);
  }

  static Future<String?> getApiToken() async {
    return await getSecureData(AppConstants.apiTokenKey);
  }

  // User Info Management - Store UserInfo array from login response
  static Future<void> saveLoginUserInfo(Map<String, dynamic> userInfo) async {
    final jsonString = jsonEncode(userInfo);
    await saveSecureData(AppConstants.loginUserInfoKey, jsonString);
  }

  static Future<Map<String, dynamic>?> getLoginUserInfo() async {
    final jsonString = await getSecureData(AppConstants.loginUserInfoKey);
    if (jsonString != null) {
      return jsonDecode(jsonString);
    }
    return null;
  }

  // Validation
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool isValidPassword(String password) {
    return password.length >= AppConstants.minPasswordLength &&
        password.length <= AppConstants.maxPasswordLength &&
        password.contains(RegExp(r'[A-Z]')) &&
        password.contains(RegExp(r'[a-z]')) &&
        password.contains(RegExp(r'[0-9]'));
  }

  static bool isValidPhoneNumber(String phoneNumber) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phoneNumber);
  }

  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }
    if (!isValidEmail(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }
    if (!isValidPassword(password)) {
      return 'Password must be ${AppConstants.minPasswordLength}-${AppConstants.maxPasswordLength} characters with uppercase, lowercase, and number';
    }
    return null;
  }

  static String? validateConfirmPassword(
    String? password,
    String? confirmPassword,
  ) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'Confirm password is required';
    }
    if (password != confirmPassword) {
      return 'Passwords do not match';
    }
    return null;
  }

  // Date and Time Formatting
  static String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static String formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  static String formatDateTime(DateTime dateTime) {
    return '${formatDate(dateTime)} ${formatTime(dateTime)}';
  }

  static String timeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }

  // Currency Formatting
  static String formatCurrency(
    double amount, {
    String currency = AppConstants.defaultCurrency,
  }) {
    return '$currency ${amount.toStringAsFixed(2)}';
  }

  // Number Formatting
  static String formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }

  // String Utils
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  static String capitalizeWords(String text) {
    if (text.isEmpty) return text;
    return text.split(' ').map((word) => capitalize(word)).join(' ');
  }

  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // Device Information
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();

    Map<String, dynamic> info = {
      'appName': packageInfo.appName,
      'packageName': packageInfo.packageName,
      'version': packageInfo.version,
      'buildNumber': packageInfo.buildNumber,
    };

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      info.addAll({
        'platform': 'Android',
        'model': androidInfo.model,
        'brand': androidInfo.brand,
        'manufacturer': androidInfo.manufacturer,
        'version': androidInfo.version.release,
        'sdkInt': androidInfo.version.sdkInt,
      });
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      info.addAll({
        'platform': 'iOS',
        'model': iosInfo.model,
        'name': iosInfo.name,
        'systemName': iosInfo.systemName,
        'systemVersion': iosInfo.systemVersion,
      });
    }

    return info;
  }

  // Error Handling
  static String getErrorMessage(dynamic error) {
    if (error == null) return AppConstants.unknownErrorMessage;

    if (error is String) return error;

    // Handle common error types
    final errorString = error.toString();

    if (errorString.contains('SocketException') ||
        errorString.contains('NetworkException') ||
        errorString.contains('No address associated with hostname')) {
      return AppConstants.noInternetMessage;
    }

    if (errorString.contains('TimeoutException') ||
        errorString.contains('timeout')) {
      return AppConstants.timeoutErrorMessage;
    }

    if (errorString.contains('FormatException') ||
        errorString.contains('validation')) {
      return AppConstants.validationErrorMessage;
    }

    return AppConstants.serverErrorMessage;
  }

  // Dialog Utils
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Yes',
    String cancelText = 'No',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  static void showInfoDialog(
    BuildContext context, {
    required String title,
    required String message,
    String okText = 'OK',
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(okText),
            ),
          ],
        );
      },
    );
  }

  // Loading Dialog
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              if (message != null) ...[
                const SizedBox(height: 16),
                Text(message),
              ],
            ],
          ),
        );
      },
    );
  }

  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context, rootNavigator: true).pop();
  }

  // Debouncer
  static Timer? _debounceTimer;

  static void debounce(
    void Function() action, {
    Duration delay = const Duration(milliseconds: 500),
  }) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
    _debounceTimer = Timer(delay, action);
  }
}
