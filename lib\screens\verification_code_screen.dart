import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/forgot_password_service.dart';
import '../utils/snackbar_utils.dart';
import '../constants.dart';
import '../routes.dart';

class VerificationCodeScreen extends StatefulWidget {
  final String username;
  final String hashedResetCode;

  const VerificationCodeScreen({
    super.key,
    required this.username,
    required this.hashedResetCode,
  });

  @override
  State<VerificationCodeScreen> createState() => _VerificationCodeScreenState();
}

class _VerificationCodeScreenState extends State<VerificationCodeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  bool _isLoading = false;

  final ForgotPasswordService _forgotPasswordService = ForgotPasswordService();

  @override
  void initState() {
    super.initState();
    _setSystemUIOverlayStyle();
  }

  void _setSystemUIOverlayStyle() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.loginPrimaryColor,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  Future<void> _verifyCode() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _forgotPasswordService.validateResetCode(
        username: widget.username,
        userResetCode: _codeController.text.trim(),
        hashedResetCode: widget.hashedResetCode,
      );

      if (response.success) {
        if (mounted) {
          SnackBarUtils.showSuccess(context, AppStrings.codeVerified);
          NavigationHelper.toResetPassword(
            context,
            username: widget.username,
            hashedResetCode: widget.hashedResetCode,
          );
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(context, response.message);
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(context, AppStrings.codeVerificationFailed);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resendCode() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _forgotPasswordService.sendVerificationEmail(
        username: widget.username,
      );

      if (response.success) {
        if (mounted) {
          SnackBarUtils.showSuccess(
            context,
            AppStrings.verificationCodeSentAgain,
          );
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(context, response.message);
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(context, AppStrings.resendCodeFailed);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            // Top blue section with back button and title
            SizedBox(
              height:
                  MediaQuery.of(context).size.height * 0.35 +
                  MediaQuery.of(context).padding.top,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.loginPrimaryColor,
                      AppColors.loginPrimaryDarkColor,
                    ],
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    20.0,
                    MediaQuery.of(context).padding.top + 10.0,
                    20.0,
                    20.0,
                  ),
                  child: Column(
                    children: [
                      // Back button row
                      Row(
                        children: [
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                          const SizedBox(width: 16),
                          const Text(
                            AppStrings.verifyCodeTitle,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              fontFamily: AppStrings.defaultFontFamily,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 40),

                      // Icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.verified_user,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Title and description
                      const Text(
                        AppStrings.enterVerificationCode,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          fontFamily: AppStrings.defaultFontFamily,
                        ),
                      ),

                      const SizedBox(height: 12),

                      Text(
                        'We sent a verification code to\n${widget.username}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppStrings.defaultFontFamily,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom white section with form
            Expanded(
              child: Container(
                width: double.infinity,
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32.0,
                    vertical: 32,
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        // Verification code field
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.inputBackgroundColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextFormField(
                            controller: _codeController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(6),
                            ],
                            decoration: const InputDecoration(
                              hintText: AppStrings.verificationCodePlaceholder,
                              hintStyle: TextStyle(
                                color: AppColors.inputHintColor,
                                fontSize: 16,
                              ),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 16,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppStrings.verificationCodeRequired;
                              }
                              if (value.length < 4) {
                                return AppStrings.enterValidCode;
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Verify code button
                        SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _verifyCode,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.loginButtonColor,
                              foregroundColor: Colors.white,
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                              disabledBackgroundColor:
                                  AppColors.primaryLightColor,
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Text(
                                    AppStrings.verifyCodeButton,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: AppStrings.defaultFontFamily,
                                    ),
                                  ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Resend code
                        TextButton(
                          onPressed: _isLoading ? null : _resendCode,
                          child: const Text(
                            AppStrings.resendCode,
                            style: TextStyle(
                              color: AppColors.loginButtonColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppStrings.defaultFontFamily,
                            ),
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Back to forgot password
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text(
                            AppStrings.backToEmailEntry,
                            style: TextStyle(
                              color: AppColors.inputHintColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: AppStrings.defaultFontFamily,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
