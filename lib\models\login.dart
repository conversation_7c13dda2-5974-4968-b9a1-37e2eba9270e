import 'package:flutter/material.dart';
import '../enums/auth_enums.dart';
import 'user_data.dart';

/// Request model for login API
class LoginRequest {
  final String username;
  final String password;
  final String applicationId;
  final String visitorId;

  LoginRequest({
    required this.username,
    required this.password,
    required this.applicationId,
    required this.visitorId,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'password': password,
      'ApplicationID': applicationId,
      'visitorID': visitorId,
    };
  }

  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      username: json['username'] ?? '',
      password: json['password'] ?? '',
      applicationId: json['ApplicationID'] ?? '',
      visitorId: json['visitorID'] ?? '',
    );
  }
}

/// Response model for login API
class LoginResponse {
  final bool error;
  final String message;
  final LoginResponseData responseData;

  LoginResponse({
    required this.error,
    required this.message,
    required this.responseData,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    debugPrint('LoginResponse: $json');
    return LoginResponse(
      error: json['error'] ?? true,
      message: json['message'] ?? '',
      responseData: LoginResponseData.fromJson(json['responseData'] ?? {}),
    );
  }

  /// Check if login was successful
  bool get success =>
      !error &&
      responseData.response.isNotEmpty &&
      responseData.response.first.responseCode == 1;

  /// Get access token from response
  String? get accessToken => responseData.token;

  /// Get user data from response
  UserData? get userData =>
      responseData.userInfo.isNotEmpty ? responseData.userInfo.first : null;

  /// Get error message from response
  String? get errorMessage => responseData.response.isNotEmpty
      ? responseData.response.first.errorMessage
      : null;

  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'message': message,
      'responseData': responseData.toJson(),
    };
  }
}

/// Response data structure for login
class LoginResponseData {
  final List<LoginApiResponse> response;
  final List<UserData> userInfo;
  final String token;

  LoginResponseData({
    required this.response,
    required this.userInfo,
    required this.token,
  });

  factory LoginResponseData.fromJson(Map<String, dynamic> json) {
    return LoginResponseData(
      response: (json['Response'] as List<dynamic>? ?? [])
          .map((e) => LoginApiResponse.fromJson(e))
          .toList(),
      userInfo: (json['UserInfo'] as List<dynamic>? ?? [])
          .map((e) => UserData.fromJson(e))
          .toList(),
      token: json['Token'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Response': response.map((e) => e.toJson()).toList(),
      'UserInfo': userInfo.map((e) => e.toJson()).toList(),
      'Token': token,
    };
  }
}

/// API response structure within login response
class LoginApiResponse {
  final int responseCode;
  final String? errorMessage;

  LoginApiResponse({required this.responseCode, this.errorMessage});

  factory LoginApiResponse.fromJson(Map<String, dynamic> json) {
    return LoginApiResponse(
      responseCode: json['ResponseCode'] ?? 0,
      errorMessage: json['ErrorMessage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'ResponseCode': responseCode, 'ErrorMessage': errorMessage};
  }

  /// Check if this response indicates success
  bool get isSuccess => responseCode == LoginResponseCode.success.value;

  /// Check if this response indicates error
  bool get isError => responseCode == LoginResponseCode.error.value;
}
