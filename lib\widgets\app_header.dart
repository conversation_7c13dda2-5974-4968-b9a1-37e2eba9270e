import 'package:flutter/material.dart';
import '../constants.dart';

class AppHeader extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? titleColor;
  final double? elevation;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final PreferredSizeWidget? bottom;

  const AppHeader({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.actions,
    this.onBackPressed,
    this.backgroundColor,
    this.titleColor,
    this.elevation,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          color: titleColor ?? AppColors.textLightColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: backgroundColor ?? AppColors.primaryColor,
      elevation: elevation ?? 0,
      centerTitle: true,
      automaticallyImplyLeading: automaticallyImplyLeading && showBackButton,
      leading: leading ?? 
        (showBackButton && automaticallyImplyLeading 
          ? IconButton(
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Colors.white,
                size: AppSizes.iconMD,
              ),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null),
      actions: actions?.map((action) {
        if (action is IconButton) {
          return IconButton(
            onPressed: action.onPressed,
            icon: action.icon,
            color: action.color ?? Colors.white,
            iconSize: action.iconSize ?? AppSizes.iconMD,
          );
        }
        return action;
      }).toList(),
      bottom: bottom,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    AppSizes.appBarHeight + (bottom?.preferredSize.height ?? 0.0)
  );
}

class SearchAppHeader extends StatelessWidget implements PreferredSizeWidget {
  final String hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchSubmitted;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final bool autofocus;

  const SearchAppHeader({
    super.key,
    this.hintText = 'Search...',
    this.controller,
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.onBackPressed,
    this.actions,
    this.backgroundColor,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? AppColors.primaryColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Colors.white,
          size: AppSizes.iconMD,
        ),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      ),
      title: TextField(
        controller: controller,
        autofocus: autofocus,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 16,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppSizes.paddingMD,
            vertical: AppSizes.paddingSM,
          ),
        ),
        onChanged: onSearchChanged,
        onSubmitted: onSearchSubmitted != null 
          ? (value) => onSearchSubmitted!()
          : null,
      ),
      actions: [
        if (controller?.text.isNotEmpty ?? false)
          IconButton(
            icon: const Icon(
              Icons.clear,
              color: Colors.white,
              size: AppSizes.iconMD,
            ),
            onPressed: () {
              controller?.clear();
              onSearchChanged?.call('');
            },
          ),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(AppSizes.appBarHeight);
}

class TabAppHeader extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Tab> tabs;
  final TabController? controller;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? titleColor;
  final Color? indicatorColor;
  final bool showBackButton;

  const TabAppHeader({
    super.key,
    required this.title,
    required this.tabs,
    this.controller,
    this.actions,
    this.onBackPressed,
    this.backgroundColor,
    this.titleColor,
    this.indicatorColor,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          color: titleColor ?? AppColors.textLightColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: backgroundColor ?? AppColors.primaryColor,
      elevation: 0,
      centerTitle: true,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton 
        ? IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: AppSizes.iconMD,
            ),
            onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
          )
        : null,
      actions: actions,
      bottom: TabBar(
        controller: controller,
        tabs: tabs,
        indicatorColor: indicatorColor ?? Colors.white,
        indicatorWeight: 2.0,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withOpacity(0.7),
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(
    AppSizes.appBarHeight + kToolbarHeight
  );
}

class ProfileAppHeader extends StatelessWidget implements PreferredSizeWidget {
  final String userName;
  final String? userEmail;
  final String? avatarUrl;
  final VoidCallback? onProfileTap;
  final List<Widget>? actions;
  final Color? backgroundColor;

  const ProfileAppHeader({
    super.key,
    required this.userName,
    this.userEmail,
    this.avatarUrl,
    this.onProfileTap,
    this.actions,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? AppColors.primaryColor,
      elevation: 0,
      automaticallyImplyLeading: false,
      title: GestureDetector(
        onTap: onProfileTap,
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: Colors.white,
              backgroundImage: avatarUrl != null 
                ? NetworkImage(avatarUrl!) 
                : null,
              child: avatarUrl == null 
                ? Text(
                    userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
            ),
            const SizedBox(width: AppSizes.paddingMD),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (userEmail != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      userEmail!,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(AppSizes.appBarHeight);
}

// Custom AppBar for specific use cases
class CustomAppHeader extends StatelessWidget implements PreferredSizeWidget {
  final Widget child;
  final double height;

  const CustomAppHeader({
    super.key,
    required this.child,
    this.height = AppSizes.appBarHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: double.infinity,
      decoration: const BoxDecoration(
        color: AppColors.primaryColor,
      ),
      child: SafeArea(
        child: child,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}