import 'package:flutter/material.dart';
import 'screens/splash_screen.dart';
import 'screens/login_screen.dart';
import 'screens/home_screen.dart';
import 'screens/forgot_password_screen.dart';
import 'screens/verification_code_screen.dart';
import 'screens/reset_password_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';
  static const String home = '/home';
  static const String forgotPassword = '/forgot-password';
  static const String verificationCode = '/verification-code';
  static const String resetPassword = '/reset-password';

  // Routes map
  static Map<String, WidgetBuilder> routes = {
    splash: (context) => const SplashScreen(),
    login: (context) => const LoginScreen(),
    home: (context) => const HomeScreen(),
    forgotPassword: (context) => const ForgotPasswordScreen(),
  };

  // Generate routes with arguments
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case verificationCode:
        final args = settings.arguments as Map<String, dynamic>?;
        if (args != null) {
          return MaterialPageRoute(
            builder: (context) => VerificationCodeScreen(
              username: args['username'] ?? '',
              hashedResetCode: args['hashedResetCode'] ?? '',
            ),
            settings: settings,
          );
        }
        return _errorRoute(settings);

      case resetPassword:
        final args = settings.arguments as Map<String, dynamic>?;
        if (args != null) {
          return MaterialPageRoute(
            builder: (context) => ResetPasswordScreen(
              username: args['username'] ?? '',
              hashedResetCode: args['hashedResetCode'] ?? '',
            ),
            settings: settings,
          );
        }
        return _errorRoute(settings);

      default:
        return null; // Let the default routes handle it
    }
  }

  // Error route for invalid navigation
  static Route<dynamic> _errorRoute(RouteSettings settings) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text(
                'Page not found',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Route: ${settings.name}',
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () =>
                    Navigator.of(context).pushReplacementNamed(login),
                child: const Text('Go to Login'),
              ),
            ],
          ),
        ),
      ),
      settings: settings,
    );
  }
}

// Route navigation helper methods
class NavigationHelper {
  // Navigate to splash
  static void toSplash(BuildContext context) {
    Navigator.of(context).pushReplacementNamed(AppRoutes.splash);
  }

  // Navigate to login
  static void toLogin(BuildContext context) {
    Navigator.of(context).pushReplacementNamed(AppRoutes.login);
  }

  // Navigate to home
  static void toHome(BuildContext context) {
    Navigator.of(context).pushReplacementNamed(AppRoutes.home);
  }

  // Navigate to forgot password
  static void toForgotPassword(BuildContext context) {
    Navigator.of(context).pushNamed(AppRoutes.forgotPassword);
  }

  // Navigate to verification code
  static void toVerificationCode(
    BuildContext context, {
    required String username,
    required String hashedResetCode,
  }) {
    Navigator.of(context).pushNamed(
      AppRoutes.verificationCode,
      arguments: {'username': username, 'hashedResetCode': hashedResetCode},
    );
  }

  // Navigate to reset password
  static void toResetPassword(
    BuildContext context, {
    required String username,
    required String hashedResetCode,
  }) {
    Navigator.of(context).pushNamed(
      AppRoutes.resetPassword,
      arguments: {'username': username, 'hashedResetCode': hashedResetCode},
    );
  }

  // Clear all and go to login
  static void clearAndGoToLogin(BuildContext context) {
    Navigator.of(
      context,
    ).pushNamedAndRemoveUntil(AppRoutes.login, (route) => false);
  }

  // Clear all and go to home
  static void clearAndGoToHome(BuildContext context) {
    Navigator.of(
      context,
    ).pushNamedAndRemoveUntil(AppRoutes.home, (route) => false);
  }
}
