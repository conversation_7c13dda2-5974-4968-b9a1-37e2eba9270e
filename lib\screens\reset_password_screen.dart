import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/forgot_password_service.dart';
import '../models/forgot_password/index.dart';
import '../utils/snackbar_utils.dart';
import '../constants.dart';
import '../routes.dart';

class ResetPasswordScreen extends StatefulWidget {
  final String username;
  final String hashedResetCode;

  const ResetPasswordScreen({
    super.key,
    required this.username,
    required this.hashedResetCode,
  });

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  PasswordStrengthResult _passwordStrength = PasswordStrengthResult(
    strength: PasswordStrength.weak,
    message: AppStrings.passwordRequired,
    score: 0.0,
  );

  final ForgotPasswordService _forgotPasswordService = ForgotPasswordService();

  @override
  void initState() {
    super.initState();
    _setSystemUIOverlayStyle();
    _passwordController.addListener(_onPasswordChanged);
  }

  void _setSystemUIOverlayStyle() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.loginPrimaryColor,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  void _onPasswordChanged() {
    setState(() {
      _passwordStrength = ForgotPasswordService.checkPasswordStrength(
        _passwordController.text,
      );
    });
  }

  @override
  void dispose() {
    _passwordController.removeListener(_onPasswordChanged);
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _resetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    if (!ForgotPasswordService.isPasswordValid(_passwordController.text)) {
      SnackBarUtils.showError(context, AppStrings.passwordRequirementsMissing);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _forgotPasswordService.resetPassword(
        username: widget.username,
        password: _passwordController.text,
      );

      if (response.success) {
        if (mounted) {
          SnackBarUtils.showSuccess(context, AppStrings.passwordResetSuccess);
          // Navigate back to login screen, clearing all previous screens
          NavigationHelper.clearAndGoToLogin(context);
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(context, response.message);
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(context, AppStrings.passwordResetFailed);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Color _getStrengthColor() {
    switch (_passwordStrength.strength) {
      case PasswordStrength.weak:
        return AppColors.passwordWeakColor;
      case PasswordStrength.medium:
        return AppColors.passwordMediumColor;
      case PasswordStrength.strong:
        return AppColors.passwordStrongColor;
    }
  }

  String _getStrengthText() {
    switch (_passwordStrength.strength) {
      case PasswordStrength.weak:
        return AppStrings.passwordStrengthWeak;
      case PasswordStrength.medium:
        return AppStrings.passwordStrengthMedium;
      case PasswordStrength.strong:
        return AppStrings.passwordStrengthStrong;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            // Top blue section with back button and title
            Container(
              constraints: BoxConstraints(
                minHeight: 280,
                maxHeight:
                    MediaQuery.of(context).size.height * 0.35 +
                    MediaQuery.of(context).padding.top,
              ),
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.loginPrimaryColor,
                      AppColors.loginPrimaryDarkColor,
                    ],
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    20.0,
                    MediaQuery.of(context).padding.top + 10.0,
                    20.0,
                    20.0,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Back button row
                      Row(
                        children: [
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                          const SizedBox(width: 16),
                          const Text(
                            AppStrings.forgotPasswordTitle,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              fontFamily: AppStrings.defaultFontFamily,
                            ),
                          ),
                        ],
                      ),

                      // Icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.lock_reset,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),

                      // Title and description
                      const Text(
                        AppStrings.createNewPassword,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          fontFamily: AppStrings.defaultFontFamily,
                        ),
                      ),
                      const Text(
                        AppStrings.resetPasswordSubtitle,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppStrings.defaultFontFamily,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom white section with form
            Expanded(
              child: Container(
                width: double.infinity,
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32.0,
                    vertical: 32,
                  ),
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // New password field
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.inputBackgroundColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: TextFormField(
                              controller: _passwordController,
                              obscureText: !_isPasswordVisible,
                              decoration: InputDecoration(
                                hintText: AppStrings.newPasswordPlaceholder,
                                hintStyle: const TextStyle(
                                  color: AppColors.inputHintColor,
                                  fontSize: 16,
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 16,
                                ),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _isPasswordVisible
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                    color: const Color(0xFF9E9E9E),
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isPasswordVisible = !_isPasswordVisible;
                                    });
                                  },
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return AppStrings.passwordRequired;
                                }
                                if (!ForgotPasswordService.isPasswordValid(
                                  value,
                                )) {
                                  return AppStrings.passwordRequirementsMissing;
                                }
                                return null;
                              },
                            ),
                          ),

                          const SizedBox(height: 8),

                          // Password strength indicator
                          Row(
                            children: [
                              // Strength bar
                              Expanded(
                                child: Container(
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: AppColors.borderColor,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                  child: FractionallySizedBox(
                                    alignment: Alignment.centerLeft,
                                    widthFactor: _passwordStrength.score,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: _getStrengthColor(),
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              // Strength text
                              Text(
                                _getStrengthText(),
                                style: TextStyle(
                                  color: _getStrengthColor(),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 4),

                          // Password requirements
                          Text(
                            _passwordStrength.message,
                            style: TextStyle(
                              color: _getStrengthColor(),
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Confirm password field
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.inputBackgroundColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: TextFormField(
                              controller: _confirmPasswordController,
                              obscureText: !_isConfirmPasswordVisible,
                              decoration: InputDecoration(
                                hintText: AppStrings.confirmPasswordPlaceholder,
                                hintStyle: const TextStyle(
                                  color: AppColors.inputHintColor,
                                  fontSize: 16,
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 16,
                                ),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _isConfirmPasswordVisible
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                    color: const Color(0xFF9E9E9E),
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isConfirmPasswordVisible =
                                          !_isConfirmPasswordVisible;
                                    });
                                  },
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return AppStrings.confirmPasswordRequired;
                                }
                                if (value != _passwordController.text) {
                                  return AppStrings.passwordsDoNotMatch;
                                }
                                return null;
                              },
                            ),
                          ),

                          const SizedBox(height: 32),

                          // Reset password button
                          SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _resetPassword,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.loginButtonColor,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                disabledBackgroundColor:
                                    AppColors.primaryLightColor,
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text(
                                      AppStrings.resetPasswordButton,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            AppStrings.defaultFontFamily,
                                      ),
                                    ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Back to login
                          Center(
                            child: TextButton(
                              onPressed: () =>
                                  NavigationHelper.clearAndGoToLogin(context),
                              child: const Text(
                                AppStrings.backToLogin,
                                style: TextStyle(
                                  color: AppColors.loginButtonColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: AppStrings.defaultFontFamily,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
