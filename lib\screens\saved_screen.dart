import 'package:flutter/material.dart';
import '../constants.dart';
import '../widgets/app_header.dart';

class SavedScreen extends StatefulWidget {
  const SavedScreen({super.key});

  @override
  State<SavedScreen> createState() => _SavedScreenState();
}

class _SavedScreenState extends State<SavedScreen> {
  final ScrollController _scrollController = ScrollController();
  List<Map<String, dynamic>> _savedItems = [];
  bool _isLoading = true;
  bool _isSelectionMode = false;
  final Set<String> _selectedItems = {};

  @override
  void initState() {
    super.initState();
    _loadSavedItems();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeader(
        title: _isSelectionMode ? '${_selectedItems.length} Selected' : 'Saved',
        showBackButton: false,
        actions: [
          if (_savedItems.isNotEmpty && !_isSelectionMode)
            IconButton(
              onPressed: _toggleSelectionMode,
              icon: const Icon(Icons.checklist),
            ),
          if (_isSelectionMode) ...[
            TextButton(
              onPressed: _cancelSelection,
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _isSelectionMode && _selectedItems.isNotEmpty
          ? _buildSelectionBottomBar()
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_savedItems.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refreshSavedItems,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppSizes.paddingMD),
        itemCount: _savedItems.length,
        itemBuilder: (context, index) {
          final item = _savedItems[index];
          final isSelected = _selectedItems.contains(item['id']);
          
          return _SavedItemCard(
            item: item,
            isSelected: isSelected,
            isSelectionMode: _isSelectionMode,
            onTap: () => _handleItemTap(item),
            onToggleSelection: () => _toggleItemSelection(item['id']),
            onRemove: () => _removeItem(item['id']),
            onAddToCart: () => _addToCart(item),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppSizes.paddingMD),
          Text(
            'Your wishlist is empty',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppSizes.paddingSM),
          Text(
            'Start adding items you love to your wishlist',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSizes.paddingLG),
          ElevatedButton(
            onPressed: () {
              // TODO: Navigate to home or shop
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingLG,
                vertical: AppSizes.paddingMD,
              ),
            ),
            child: const Text(
              'Start Shopping',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionBottomBar() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMD),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _removeSelectedItems,
              icon: const Icon(Icons.delete_outline),
              label: const Text('Remove'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.errorColor,
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingMD),
              ),
            ),
          ),
          const SizedBox(width: AppSizes.paddingMD),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _addSelectedToCart,
              icon: const Icon(Icons.shopping_cart_outlined),
              label: const Text('Add to Cart'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingMD),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadSavedItems() async {
    setState(() => _isLoading = true);
    
    try {
      // TODO: Load from API or local storage
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _savedItems = _mockSavedItems;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      // TODO: Show error message
    }
  }

  Future<void> _refreshSavedItems() async {
    await _loadSavedItems();
  }

  void _handleItemTap(Map<String, dynamic> item) {
    if (_isSelectionMode) {
      _toggleItemSelection(item['id']);
    } else {
      // TODO: Navigate to product details
    }
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedItems.clear();
      }
    });
  }

  void _cancelSelection() {
    setState(() {
      _isSelectionMode = false;
      _selectedItems.clear();
    });
  }

  void _toggleItemSelection(String itemId) {
    setState(() {
      if (_selectedItems.contains(itemId)) {
        _selectedItems.remove(itemId);
      } else {
        _selectedItems.add(itemId);
      }
    });
  }

  void _removeItem(String itemId) async {
    // TODO: Remove from API/local storage
    setState(() {
      _savedItems.removeWhere((item) => item['id'] == itemId);
    });
    
    // TODO: Show undo snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Item removed from wishlist'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _removeSelectedItems() async {
    // TODO: Remove selected items from API/local storage
    setState(() {
      _savedItems.removeWhere((item) => _selectedItems.contains(item['id']));
      _selectedItems.clear();
      _isSelectionMode = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Selected items removed from wishlist'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addToCart(Map<String, dynamic> item) async {
    // TODO: Add to cart API call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${item['name']} added to cart'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View Cart',
          onPressed: () {
            // TODO: Navigate to cart
          },
        ),
      ),
    );
  }

  void _addSelectedToCart() async {
    final selectedItemsData = _savedItems
        .where((item) => _selectedItems.contains(item['id']))
        .toList();
    
    // TODO: Add selected items to cart
    setState(() {
      _selectedItems.clear();
      _isSelectionMode = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${selectedItemsData.length} items added to cart'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View Cart',
          onPressed: () {
            // TODO: Navigate to cart
          },
        ),
      ),
    );
  }

  // Mock data - replace with actual API calls
  final List<Map<String, dynamic>> _mockSavedItems = [
    {
      'id': '1',
      'name': 'Wireless Bluetooth Headphones',
      'price': 99.99,
      'originalPrice': 129.99,
      'image': 'https://via.placeholder.com/200x200',
      'rating': 4.5,
      'reviews': 128,
      'category': 'Electronics',
      'inStock': true,
      'savedDate': '2024-01-15',
    },
    {
      'id': '2',
      'name': 'Smart Fitness Watch',
      'price': 199.99,
      'image': 'https://via.placeholder.com/200x200',
      'rating': 4.8,
      'reviews': 256,
      'category': 'Electronics',
      'inStock': true,
      'savedDate': '2024-01-10',
    },
    {
      'id': '3',
      'name': 'Premium Running Shoes',
      'price': 79.99,
      'originalPrice': 99.99,
      'image': 'https://via.placeholder.com/200x200',
      'rating': 4.3,
      'reviews': 89,
      'category': 'Sports',
      'inStock': false,
      'savedDate': '2024-01-05',
    },
  ];
}

class _SavedItemCard extends StatelessWidget {
  final Map<String, dynamic> item;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback onTap;
  final VoidCallback onToggleSelection;
  final VoidCallback onRemove;
  final VoidCallback onAddToCart;

  const _SavedItemCard({
    required this.item,
    required this.isSelected,
    required this.isSelectionMode,
    required this.onTap,
    required this.onToggleSelection,
    required this.onRemove,
    required this.onAddToCart,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSizes.paddingMD),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSizes.radiusMD),
          border: isSelected
              ? Border.all(color: AppColors.primaryColor, width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingMD),
          child: Row(
            children: [
              if (isSelectionMode) ...[
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => onToggleSelection(),
                  activeColor: AppColors.primaryColor,
                ),
                const SizedBox(width: AppSizes.paddingSM),
              ],
              
              // Product Image
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(AppSizes.radiusSM),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Icon(
                        Icons.image,
                        size: 32,
                        color: Colors.grey[400],
                      ),
                    ),
                    if (!item['inStock'])
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(AppSizes.radiusSM),
                        ),
                        child: const Center(
                          child: Text(
                            'Out of\nStock',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(width: AppSizes.paddingMD),
              
              // Product Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 14,
                          color: Colors.amber[600],
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${item['rating']} (${item['reviews']})',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          '\$${item['price']}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryColor,
                          ),
                        ),
                        if (item['originalPrice'] != null) ...[
                          const SizedBox(width: 8),
                          Text(
                            '\$${item['originalPrice']}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        if (item['inStock']) ...[
                          Expanded(
                            child: ElevatedButton(
                              onPressed: onAddToCart,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primaryColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: AppSizes.paddingSM,
                                ),
                                minimumSize: const Size(0, 32),
                              ),
                              child: const Text(
                                'Add to Cart',
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSizes.paddingSM),
                        ] else ...[
                          Expanded(
                            child: OutlinedButton(
                              onPressed: null,
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: AppSizes.paddingSM,
                                ),
                                minimumSize: const Size(0, 32),
                              ),
                              child: const Text(
                                'Out of Stock',
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSizes.paddingSM),
                        ],
                        IconButton(
                          onPressed: onRemove,
                          icon: const Icon(
                            Icons.delete_outline,
                            color: AppColors.errorColor,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}