import '../models/property_models.dart';
import '../services/api_service.dart';
import '../utils.dart';

class PropertyService {
  static final PropertyService _instance = PropertyService._internal();
  factory PropertyService() => _instance;
  PropertyService._internal();

  final ApiService _apiService = ApiService();

  Future<ApiResponse<PropertyDetailResponse>> getPropertyDetails({
    required String propertyId,
    required String loginEntityId,
    required String applicationId,
  }) async {
    try {
      final response = await _apiService.get<PropertyDetailResponse>(
        'property/propertyDetails/$propertyId/$loginEntityId',
        queryParameters: {'applicationID': applicationId},
        parser: (data) => PropertyDetailResponse.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<PropertyDetailResponse>(
        success: false,
        message: AppUtils.getErrorMessage(e),
        statusCode: 500,
        error: e.toString(),
      );
    }
  }
}
