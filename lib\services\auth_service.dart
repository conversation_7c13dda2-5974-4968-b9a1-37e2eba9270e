import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import '../models/auth_models.dart';
import '../services/api_service.dart';
import '../utils.dart';
import '../constants.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();

  Future<ApiResponse<ValidateUserResponse>> validateUsername(
    String username,
  ) async {
    try {
      final request = ValidateUserRequest(username: username);

      final response = await _apiService.post<ValidateUserResponse>(
        'login/validateUsername',
        data: request.toJson(),
        parser: (data) => ValidateUserResponse.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<ValidateUserResponse>(
        success: false,
        message: AppUtils.getErrorMessage(e),
        statusCode: 500,
        error: e.toString(),
      );
    }
  }

  Future<ApiResponse<LoginResponse>> login({
    required String username,
    required String password,
  }) async {
    try {
      final visitorId = await _getOrCreateVisitorId();

      final request = LoginRequest(
        username: username,
        password: password,
        applicationId: '4', // Default application ID as per API specification
        visitorId: visitorId,
      );

      final response = await _apiService.post<LoginResponse>(
        'user/login',
        data: request.toJson(),
        parser: (data) => LoginResponse.fromJson(data),
      );

      // If login successful, save Token and UserInfo separately
      if (response.success && response.data != null) {
        final loginData = response.data!;

        // Save Token from response for API calls
        if (loginData.accessToken != null &&
            loginData.accessToken!.isNotEmpty) {
          await AppUtils.saveApiToken(loginData.accessToken!);
        }

        // Save UserInfo from response
        if (loginData.userData != null) {
          await AppUtils.saveLoginUserInfo(loginData.userData!.toJson());
        }
      }

      return response;
    } catch (e) {
      return ApiResponse<LoginResponse>(
        success: false,
        message: AppUtils.getErrorMessage(e),
        statusCode: 500,
        error: e.toString(),
      );
    }
  }

  Future<bool> logout() async {
    try {
      await AppUtils.clearTokens();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> isLoggedIn() async {
    final token = await AppUtils.getApiToken();
    return token != null && token.isNotEmpty;
  }

  Future<UserData?> getCurrentUser() async {
    final userInfo = await AppUtils.getLoginUserInfo();
    if (userInfo != null) {
      return UserData.fromJson(userInfo);
    }
    return null;
  }

  Future<String> _getOrCreateVisitorId() async {
    String? visitorId = await AppUtils.getSecureData(AppConstants.visitorIdKey);

    if (visitorId == null) {
      // Generate a unique visitor ID based on device info
      try {
        final deviceInfoPlugin = DeviceInfoPlugin();
        String deviceIdentifier;

        if (Platform.isAndroid) {
          final androidInfo = await deviceInfoPlugin.androidInfo;
          deviceIdentifier =
              '${androidInfo.id}_${androidInfo.model}_${androidInfo.brand}';
        } else if (Platform.isIOS) {
          final iosInfo = await deviceInfoPlugin.iosInfo;
          deviceIdentifier = '${iosInfo.identifierForVendor}_${iosInfo.model}';
        } else {
          // Fallback for other platforms
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          deviceIdentifier = 'unknown_device_$timestamp';
        }

        // Create a visitor ID that's unique to this device
        visitorId =
            'retail_app_${deviceIdentifier.replaceAll(' ', '_').replaceAll('-', '_')}';
        await AppUtils.saveSecureData(AppConstants.visitorIdKey, visitorId);
      } catch (e) {
        // Fallback if device info fails
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        visitorId = 'retail_app_fallback_$timestamp';
        await AppUtils.saveSecureData(AppConstants.visitorIdKey, visitorId);
      }
    } else {}

    return visitorId;
  }
}
