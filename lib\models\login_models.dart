/// Backward compatibility file for login models
///
/// This file maintains backward compatibility with existing code
/// while redirecting to the new organized model structure.
///
/// @deprecated Use '../models/auth_models.dart' instead
library login_models;

export 'auth_models.dart';

// Provide backward compatibility aliases
import 'validate_user.dart' as validate;
import 'login.dart' as login;
import 'user_data.dart' as user;

/// @deprecated Use ValidateUserRequest instead
typedef ValidateUsernameRequest = validate.ValidateUserRequest;

/// @deprecated Use ValidateUserResponse instead
typedef ValidateUsernameResponse = validate.ValidateUserResponse;

/// @deprecated Use ValidateUserResponseData instead
typedef ValidateUsernameResponseData = validate.ValidateUserResponseData;

/// @deprecated Use LoginStatus from validate_user.dart instead
typedef LoginStatus = validate.LoginStatus;

/// Re-export login models with same names
typedef LoginRequest = login.LoginRequest;
typedef LoginResponse = login.LoginResponse;
typedef LoginResponseData = login.LoginResponseData;
typedef LoginApiResponse = login.LoginApiResponse;
typedef UserData = user.UserData;
