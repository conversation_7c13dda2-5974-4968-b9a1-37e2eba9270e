/// User data model representing user information from login response
class UserData {
  final String username;
  final int loginId;
  final String personName;
  final String firstName;
  final String lastName;
  final String email;
  final int companyId;
  final String? companyName;
  final int roleId;
  final int? tenantId;
  final int personId;
  final int entityId;
  final int countryId;
  final int metroId;
  final double metroCentroidLat;
  final double metroCentroidLong;
  final String roleName;
  final int unitId;
  final String mainPhotoUrl;
  final int failedAttemptsCount;
  final String dateFormat;
  final int isMember;
  final String metroSkylineImageUrl;
  final int canImpersonateUser;
  final int canUpdateRole;
  final int pilotMode;
  final String? officePhone;
  final String driveToolVersion;
  final String? companyFloorNumber;
  final String? companyAddress;
  final String? companyMainPhotoUrl;
  final String? companyCityName;
  final String? companyZipCode;
  final String? phoneNumberRegEx;
  final String? phoneNumberPattern;
  final String? phoneNumberMask;
  final String unitDisplayTextSize;
  final String unitDisplayTextLength;
  final String? canEditInPublic;
  final String mobileNumberPrefix;
  final String mobileNumberMask;
  final int showSavedSearch;
  final int showRegisteredLease;
  final int companyTierId;
  final int ultimateCompanyId;
  final String ultimateCompanyName;
  final int stateId;
  final int isLimitedAccess;
  final int hasMarketBriefAccess;
  final int enforceTenantExportLimit;
  final int enableSaleExport;
  final int enableLeaseExport;
  final int forcefullyResetPassword;
  final int enableV2Search;
  final int enforceProExportLimit;

  UserData({
    required this.username,
    required this.loginId,
    required this.personName,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.companyId,
    this.companyName,
    required this.roleId,
    this.tenantId,
    required this.personId,
    required this.entityId,
    required this.countryId,
    required this.metroId,
    required this.metroCentroidLat,
    required this.metroCentroidLong,
    required this.roleName,
    required this.unitId,
    required this.mainPhotoUrl,
    required this.failedAttemptsCount,
    required this.dateFormat,
    required this.isMember,
    required this.metroSkylineImageUrl,
    required this.canImpersonateUser,
    required this.canUpdateRole,
    required this.pilotMode,
    this.officePhone,
    required this.driveToolVersion,
    this.companyFloorNumber,
    this.companyAddress,
    this.companyMainPhotoUrl,
    this.companyCityName,
    this.companyZipCode,
    this.phoneNumberRegEx,
    this.phoneNumberPattern,
    this.phoneNumberMask,
    required this.unitDisplayTextSize,
    required this.unitDisplayTextLength,
    this.canEditInPublic,
    required this.mobileNumberPrefix,
    required this.mobileNumberMask,
    required this.showSavedSearch,
    required this.showRegisteredLease,
    required this.companyTierId,
    required this.ultimateCompanyId,
    required this.ultimateCompanyName,
    required this.stateId,
    required this.isLimitedAccess,
    required this.hasMarketBriefAccess,
    required this.enforceTenantExportLimit,
    required this.enableSaleExport,
    required this.enableLeaseExport,
    required this.forcefullyResetPassword,
    required this.enableV2Search,
    required this.enforceProExportLimit,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      username: json['Username'] ?? '',
      loginId: json['LoginID'] ?? 0,
      personName: json['PersonName'] ?? '',
      firstName: json['FirstName'] ?? '',
      lastName: json['LastName'] ?? '',
      email: json['Email'] ?? '',
      companyId: json['CompanyID'] ?? 0,
      companyName: json['CompanyName'],
      roleId: json['RoleID'] ?? 0,
      tenantId: json['TenantID'],
      personId: json['PersonID'] ?? 0,
      entityId: json['EntityID'] ?? 0,
      countryId: json['CountryID'] ?? 0,
      metroId: json['MetroID'] ?? 0,
      metroCentroidLat: (json['MetroCentroidLat'] ?? 0).toDouble(),
      metroCentroidLong: (json['MetroCentroidLong'] ?? 0).toDouble(),
      roleName: json['RoleName'] ?? '',
      unitId: json['UnitId'] ?? 0,
      mainPhotoUrl: json['MainPhotoUrl'] ?? '',
      failedAttemptsCount: json['FailedAttemptsCount'] ?? 0,
      dateFormat: json['DateFormat'] ?? '',
      isMember: json['IsMember'] ?? 0,
      metroSkylineImageUrl: json['MetroSkylineImageUrl'] ?? '',
      canImpersonateUser: json['CanImpersonateUser'] ?? 0,
      canUpdateRole: json['CanUpdateRole'] ?? 0,
      pilotMode: json['PilotMode'] ?? 0,
      officePhone: json['OfficePhone'],
      driveToolVersion: json['DriveToolVersion'] ?? '',
      companyFloorNumber: json['CompanyFloorNumber'],
      companyAddress: json['CompanyAddress'],
      companyMainPhotoUrl: json['CompanyMainPhotoUrl'],
      companyCityName: json['CompanyCityName'],
      companyZipCode: json['CompanyZipCode'],
      phoneNumberRegEx: json['PhoneNumberRegEx'],
      phoneNumberPattern: json['PhoneNumberPattern'],
      phoneNumberMask: json['PhoneNumberMask'],
      unitDisplayTextSize: json['UnitDisplayTextSize'] ?? '',
      unitDisplayTextLength: json['UnitDisplayTextLength'] ?? '',
      canEditInPublic: json['CanEditInPublic'],
      mobileNumberPrefix: json['MobileNumberPrefix'] ?? '',
      mobileNumberMask: json['MobileNumberMask'] ?? '',
      showSavedSearch: json['showSavedSearch'] ?? 0,
      showRegisteredLease: json['ShowRegisteredLease'] ?? 0,
      companyTierId: json['CompanyTierID'] ?? 0,
      ultimateCompanyId: json['UltimateCompanyID'] ?? 0,
      ultimateCompanyName: json['UltimateCompanyName'] ?? '',
      stateId: json['StateID'] ?? 0,
      isLimitedAccess: json['IsLimitedAccess'] ?? 0,
      hasMarketBriefAccess: json['HasMarketBriefAccess'] ?? 0,
      enforceTenantExportLimit: json['EnforceTenantExportLimit'] ?? 0,
      enableSaleExport: json['EnableSaleExport'] ?? 0,
      enableLeaseExport: json['EnableLeaseExport'] ?? 0,
      forcefullyResetPassword: json['ForcefullyResetPassword'] ?? 0,
      enableV2Search: json['EnableV2Search'] ?? 0,
      enforceProExportLimit: json['EnforceProExportLimit'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Username': username,
      'LoginID': loginId,
      'PersonName': personName,
      'FirstName': firstName,
      'LastName': lastName,
      'Email': email,
      'CompanyID': companyId,
      'CompanyName': companyName,
      'RoleID': roleId,
      'TenantID': tenantId,
      'PersonID': personId,
      'EntityID': entityId,
      'CountryID': countryId,
      'MetroID': metroId,
      'MetroCentroidLat': metroCentroidLat,
      'MetroCentroidLong': metroCentroidLong,
      'RoleName': roleName,
      'UnitId': unitId,
      'MainPhotoUrl': mainPhotoUrl,
      'FailedAttemptsCount': failedAttemptsCount,
      'DateFormat': dateFormat,
      'IsMember': isMember,
      'MetroSkylineImageUrl': metroSkylineImageUrl,
      'CanImpersonateUser': canImpersonateUser,
      'CanUpdateRole': canUpdateRole,
      'PilotMode': pilotMode,
      'OfficePhone': officePhone,
      'DriveToolVersion': driveToolVersion,
      'CompanyFloorNumber': companyFloorNumber,
      'CompanyAddress': companyAddress,
      'CompanyMainPhotoUrl': companyMainPhotoUrl,
      'CompanyCityName': companyCityName,
      'CompanyZipCode': companyZipCode,
      'PhoneNumberRegEx': phoneNumberRegEx,
      'PhoneNumberPattern': phoneNumberPattern,
      'PhoneNumberMask': phoneNumberMask,
      'UnitDisplayTextSize': unitDisplayTextSize,
      'UnitDisplayTextLength': unitDisplayTextLength,
      'CanEditInPublic': canEditInPublic,
      'MobileNumberPrefix': mobileNumberPrefix,
      'MobileNumberMask': mobileNumberMask,
      'showSavedSearch': showSavedSearch,
      'ShowRegisteredLease': showRegisteredLease,
      'CompanyTierID': companyTierId,
      'UltimateCompanyID': ultimateCompanyId,
      'UltimateCompanyName': ultimateCompanyName,
      'StateID': stateId,
      'IsLimitedAccess': isLimitedAccess,
      'HasMarketBriefAccess': hasMarketBriefAccess,
      'EnforceTenantExportLimit': enforceTenantExportLimit,
      'EnableSaleExport': enableSaleExport,
      'EnableLeaseExport': enableLeaseExport,
      'ForcefullyResetPassword': forcefullyResetPassword,
      'EnableV2Search': enableV2Search,
      'EnforceProExportLimit': enforceProExportLimit,
    };
  }

  /// Get user's full name
  String get fullName =>
      personName.isNotEmpty ? personName : '$firstName $lastName';

  /// Get user's ID as string
  String get id => entityId.toString();

  /// Check if user is active member
  bool get isActive => isMember == 1;

  /// Check if user needs to reset password
  bool get needsPasswordReset => forcefullyResetPassword == 1;

  /// Get user's display name (priority: personName, firstName lastName, username)
  String get displayName {
    if (personName.isNotEmpty) return personName;
    if (firstName.isNotEmpty || lastName.isNotEmpty) {
      return '$firstName $lastName'.trim();
    }
    return username;
  }

  /// Get company display name
  String get companyDisplayName => companyName ?? 'Unknown Company';
}
