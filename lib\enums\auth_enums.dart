/// Enum for login status codes used in username validation
enum LoginStatusCode {
  success(1),
  error(-1);

  const LoginStatusCode(this.value);
  final int value;

  static LoginStatusCode fromValue(int value) {
    switch (value) {
      case 1:
        return LoginStatusCode.success;
      case -1:
        return LoginStatusCode.error;
      default:
        return LoginStatusCode.error;
    }
  }
}

/// Enum for login response codes used in authentication
enum LoginResponseCode {
  success(1),
  error(-1);

  const LoginResponseCode(this.value);
  final int value;

  static LoginResponseCode fromValue(int value) {
    switch (value) {
      case 1:
        return LoginResponseCode.success;
      case -1:
        return LoginResponseCode.error;
      default:
        return LoginResponseCode.error;
    }
  }
}

/// Enum for different authentication states
enum AuthenticationState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Enum for password reset states
enum PasswordResetState {
  initial,
  emailSent,
  codeVerified,
  passwordReset,
  error,
}

enum PasswordStrength { weak, medium, strong }
