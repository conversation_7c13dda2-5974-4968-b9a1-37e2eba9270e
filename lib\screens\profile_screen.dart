import 'package:flutter/material.dart';
import '../constants.dart';
import '../widgets/app_header.dart';
import '../utils.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final ScrollController _scrollController = ScrollController();
  Map<String, dynamic>? _userData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ProfileAppHeader(
        userName: _userData?['name'] ?? 'User',
        userEmail: _userData?['email'],
        avatarUrl: _userData?['avatar'],
        onProfileTap: _editProfile,
        actions: [
          IconButton(
            onPressed: _showSettings,
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _refreshUserData,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Quick Stats
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(AppSizes.paddingMD),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppSizes.radiusMD),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingMD),
                child: Row(
                  children: [
                    _buildStatItem(
                      title: 'Orders',
                      value: _userData?['totalOrders']?.toString() ?? '0',
                      icon: Icons.shopping_bag_outlined,
                      onTap: () => _navigateToOrders(),
                    ),
                    const VerticalDivider(),
                    _buildStatItem(
                      title: 'Wishlist',
                      value: _userData?['wishlistCount']?.toString() ?? '0',
                      icon: Icons.favorite_outline,
                      onTap: () => _navigateToWishlist(),
                    ),
                    const VerticalDivider(),
                    _buildStatItem(
                      title: 'Reviews',
                      value: _userData?['reviewCount']?.toString() ?? '0',
                      icon: Icons.star_outline,
                      onTap: () => _navigateToReviews(),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Menu Items
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingMD,
              ),
              child: Column(
                children: [
                  _buildMenuSection(
                    title: 'Account',
                    items: [
                      _MenuItem(
                        icon: Icons.person_outline,
                        title: 'Edit Profile',
                        onTap: _editProfile,
                      ),
                      _MenuItem(
                        icon: Icons.location_on_outlined,
                        title: 'Addresses',
                        onTap: _manageAddresses,
                      ),
                      _MenuItem(
                        icon: Icons.payment_outlined,
                        title: 'Payment Methods',
                        onTap: _managePaymentMethods,
                      ),
                      _MenuItem(
                        icon: Icons.security_outlined,
                        title: 'Security',
                        onTap: _manageSecuritySettings,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingLG),

                  _buildMenuSection(
                    title: 'Orders & Shopping',
                    items: [
                      _MenuItem(
                        icon: Icons.history,
                        title: 'Order History',
                        onTap: _navigateToOrders,
                      ),
                      _MenuItem(
                        icon: Icons.local_shipping_outlined,
                        title: 'Track Orders',
                        onTap: _trackOrders,
                      ),
                      _MenuItem(
                        icon: Icons.assignment_return_outlined,
                        title: 'Returns & Refunds',
                        onTap: _manageReturns,
                      ),
                      _MenuItem(
                        icon: Icons.star_outline,
                        title: 'Reviews & Ratings',
                        onTap: _navigateToReviews,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingLG),

                  _buildMenuSection(
                    title: 'Support & Legal',
                    items: [
                      _MenuItem(
                        icon: Icons.help_outline,
                        title: 'Help Center',
                        onTap: _openHelpCenter,
                      ),
                      _MenuItem(
                        icon: Icons.chat_bubble_outline,
                        title: 'Contact Support',
                        onTap: _contactSupport,
                      ),
                      _MenuItem(
                        icon: Icons.description_outlined,
                        title: 'Terms & Conditions',
                        onTap: _openTermsAndConditions,
                      ),
                      _MenuItem(
                        icon: Icons.privacy_tip_outlined,
                        title: 'Privacy Policy',
                        onTap: _openPrivacyPolicy,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingLG),

                  _buildMenuSection(
                    title: 'App',
                    items: [
                      _MenuItem(
                        icon: Icons.notifications_outlined,
                        title: 'Notifications',
                        onTap: _manageNotifications,
                      ),
                      _MenuItem(
                        icon: Icons.language_outlined,
                        title: 'Language',
                        onTap: _changeLanguage,
                      ),
                      _MenuItem(
                        icon: Icons.info_outline,
                        title: 'About',
                        onTap: _showAbout,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingLG),

                  // Logout Button
                  Container(
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(
                      vertical: AppSizes.paddingMD,
                    ),
                    child: OutlinedButton.icon(
                      onPressed: _logout,
                      icon: const Icon(
                        Icons.logout,
                        color: AppColors.errorColor,
                      ),
                      label: const Text(
                        'Logout',
                        style: TextStyle(
                          color: AppColors.errorColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.errorColor),
                        padding: const EdgeInsets.symmetric(
                          vertical: AppSizes.paddingMD,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: AppSizes.paddingXL),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Icon(icon, size: AppSizes.iconLG, color: AppColors.primaryColor),
            const SizedBox(height: AppSizes.paddingSM),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuSection({
    required String title,
    required List<_MenuItem> items,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(
              AppSizes.paddingMD,
              AppSizes.paddingMD,
              AppSizes.paddingMD,
              AppSizes.paddingSM,
            ),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimaryColor,
              ),
            ),
          ),
          ...items.map((item) => _buildMenuItem(item)).toList(),
        ],
      ),
    );
  }

  Widget _buildMenuItem(_MenuItem item) {
    return ListTile(
      leading: Icon(item.icon, color: AppColors.textSecondaryColor),
      title: Text(item.title),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.textSecondaryColor,
      ),
      onTap: item.onTap,
    );
  }

  Future<void> _loadUserData() async {
    setState(() => _isLoading = true);

    try {
      // TODO: Load user data from API
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _userData = _mockUserData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      AppUtils.showErrorToast('Failed to load user data');
    }
  }

  Future<void> _refreshUserData() async {
    await _loadUserData();
  }

  void _editProfile() {
    // TODO: Navigate to edit profile screen
  }

  void _showSettings() {
    // TODO: Navigate to settings screen
  }

  void _navigateToOrders() {
    // TODO: Navigate to orders screen
  }

  void _navigateToWishlist() {
    // TODO: Navigate to wishlist/saved screen
  }

  void _navigateToReviews() {
    // TODO: Navigate to reviews screen
  }

  void _manageAddresses() {
    // TODO: Navigate to address management screen
  }

  void _managePaymentMethods() {
    // TODO: Navigate to payment methods screen
  }

  void _manageSecuritySettings() {
    // TODO: Navigate to security settings screen
  }

  void _trackOrders() {
    // TODO: Navigate to order tracking screen
  }

  void _manageReturns() {
    // TODO: Navigate to returns management screen
  }

  void _openHelpCenter() {
    // TODO: Navigate to help center or open web page
  }

  void _contactSupport() {
    // TODO: Show contact options (email, chat, phone)
  }

  void _openTermsAndConditions() {
    // TODO: Open terms and conditions page
  }

  void _openPrivacyPolicy() {
    // TODO: Open privacy policy page
  }

  void _manageNotifications() {
    // TODO: Navigate to notification settings
  }

  void _changeLanguage() {
    // TODO: Show language selection dialog
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${AppConstants.appName} v${AppConstants.appVersion}'),
            const SizedBox(height: AppSizes.paddingSM),
            const Text('Your premier shopping destination'),
            const SizedBox(height: AppSizes.paddingSM),
            const Text('© 2024 Retail App. All rights reserved.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _logout() async {
    final confirmed = await AppUtils.showConfirmationDialog(
      context,
      title: 'Logout',
      message: 'Are you sure you want to logout?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
    );

    if (confirmed) {
      try {
        AppUtils.showLoadingDialog(context, message: 'Logging out...');

        // TODO: Call logout API
        await Future.delayed(const Duration(seconds: 1));

        // Clear stored user data
        await AppUtils.clearTokens();

        if (mounted) {
          AppUtils.hideLoadingDialog(context);
          AppUtils.showSuccessToast(AppConstants.logoutSuccessMessage);

          // TODO: Navigate to login screen and clear navigation stack
          Navigator.of(context).pushReplacementNamed('/login');
        }
      } catch (e) {
        if (mounted) {
          AppUtils.hideLoadingDialog(context);
          AppUtils.showErrorToast('Logout failed. Please try again.');
        }
      }
    }
  }

  // Mock user data - replace with actual API call
  final Map<String, dynamic> _mockUserData = {
    'id': '1',
    'name': 'John Doe',
    'email': '<EMAIL>',
    'phone': '+1234567890',
    'avatar': null,
    'totalOrders': 12,
    'wishlistCount': 8,
    'reviewCount': 5,
    'memberSince': '2023-01-15',
    'address': {
      'street': '123 Main St',
      'city': 'New York',
      'state': 'NY',
      'zipCode': '10001',
      'country': 'USA',
    },
  };
}

class _MenuItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _MenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
  });
}
