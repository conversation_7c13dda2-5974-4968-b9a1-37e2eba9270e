import 'package:flutter/material.dart';
import '../constants.dart';
import '../widgets/app_header.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<Map<String, dynamic>> _searchResults = [];
  List<String> _recentSearches = [];
  bool _isLoading = false;
  bool _hasSearched = false;

  @override
  void initState() {
    super.initState();
    _loadRecentSearches();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SearchAppHeader(
        controller: _searchController,
        onSearchChanged: _onSearchChanged,
        onSearchSubmitted: _onSearchSubmitted,
        autofocus: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (!_hasSearched) {
      return _buildInitialView();
    }

    if (_searchResults.isEmpty) {
      return _buildEmptyResults();
    }

    return _buildSearchResults();
  }

  Widget _buildInitialView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingMD),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Popular Categories
          const Text(
            'Popular Categories',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSizes.paddingMD),
          Wrap(
            spacing: AppSizes.paddingSM,
            runSpacing: AppSizes.paddingSM,
            children: _popularCategories.map((category) {
              return GestureDetector(
                onTap: () => _searchCategory(category),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingMD,
                    vertical: AppSizes.paddingSM,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryLightColor,
                    borderRadius: BorderRadius.circular(AppSizes.radiusMD),
                  ),
                  child: Text(
                    category,
                    style: const TextStyle(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          if (_recentSearches.isNotEmpty) ...[
            const SizedBox(height: AppSizes.paddingXL),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Searches',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: _clearRecentSearches,
                  child: const Text('Clear All'),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingSM),
            Column(
              children: _recentSearches.map((search) {
                return ListTile(
                  leading: const Icon(Icons.history),
                  title: Text(search),
                  trailing: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => _removeRecentSearch(search),
                  ),
                  onTap: () => _performSearch(search),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppSizes.paddingMD),
          Text(
            'No results found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: AppSizes.paddingSM),
          Text(
            'Try searching with different keywords',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Column(
      children: [
        // Search Results Header
        Container(
          padding: const EdgeInsets.all(AppSizes.paddingMD),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_searchResults.length} results found',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              IconButton(
                onPressed: _showFilterOptions,
                icon: const Icon(Icons.tune),
              ),
            ],
          ),
        ),
        
        // Results List
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMD),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final product = _searchResults[index];
              return _SearchResultCard(product: product);
            },
          ),
        ),
      ],
    );
  }

  void _onSearchChanged(String query) {
    // Implement debounced search
    if (query.isNotEmpty) {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        _performSearch(query);
      });
    }
  }

  void _onSearchSubmitted() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      _performSearch(query);
    }
  }

  Timer? _debounceTimer;

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _hasSearched = true;
    });

    // Add to recent searches
    _addToRecentSearches(query);
    _searchController.text = query;

    try {
      // TODO: Implement actual search API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      // Mock search results
      final results = _mockSearchResults.where((product) =>
        product['name'].toLowerCase().contains(query.toLowerCase()) ||
        product['category'].toLowerCase().contains(query.toLowerCase())
      ).toList();

      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isLoading = false;
      });
    }
  }

  void _searchCategory(String category) {
    _searchController.text = category;
    _performSearch(category);
  }

  void _loadRecentSearches() {
    // TODO: Load from local storage
    setState(() {
      _recentSearches = ['headphones', 'smartphone', 'laptop'];
    });
  }

  void _addToRecentSearches(String search) {
    setState(() {
      _recentSearches.removeWhere((item) => item.toLowerCase() == search.toLowerCase());
      _recentSearches.insert(0, search);
      if (_recentSearches.length > 10) {
        _recentSearches = _recentSearches.take(10).toList();
      }
    });
    // TODO: Save to local storage
  }

  void _removeRecentSearch(String search) {
    setState(() {
      _recentSearches.remove(search);
    });
    // TODO: Update local storage
  }

  void _clearRecentSearches() {
    setState(() {
      _recentSearches.clear();
    });
    // TODO: Clear from local storage
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => const _FilterBottomSheet(),
    );
  }

  // Sample data
  final List<String> _popularCategories = [
    'Electronics',
    'Fashion',
    'Home & Garden',
    'Sports',
    'Books',
    'Beauty',
    'Automotive',
    'Toys',
  ];

  final List<Map<String, dynamic>> _mockSearchResults = [
    {
      'id': '1',
      'name': 'Wireless Headphones',
      'category': 'Electronics',
      'price': 99.99,
      'image': 'https://via.placeholder.com/200x200',
      'rating': 4.5,
      'reviews': 128,
    },
    {
      'id': '2',
      'name': 'Smartphone Pro',
      'category': 'Electronics',
      'price': 699.99,
      'image': 'https://via.placeholder.com/200x200',
      'rating': 4.8,
      'reviews': 256,
    },
    {
      'id': '3',
      'name': 'Gaming Laptop',
      'category': 'Electronics',
      'price': 1299.99,
      'image': 'https://via.placeholder.com/200x200',
      'rating': 4.6,
      'reviews': 89,
    },
    // Add more mock data as needed
  ];
}

class _SearchResultCard extends StatelessWidget {
  final Map<String, dynamic> product;

  const _SearchResultCard({required this.product});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to product details
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSizes.paddingMD),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSizes.radiusMD),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Product Image
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: const BorderRadius.horizontal(
                  left: Radius.circular(AppSizes.radiusMD),
                ),
              ),
              child: Icon(
                Icons.image,
                size: 40,
                color: Colors.grey[400],
              ),
            ),
            
            // Product Info
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingMD),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product['category'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16,
                          color: Colors.amber[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${product['rating']} (${product['reviews']})',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '\$${product['price']}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Favorite Button
            IconButton(
              onPressed: () {
                // TODO: Toggle wishlist
              },
              icon: const Icon(Icons.favorite_border),
            ),
          ],
        ),
      ),
    );
  }
}

class _FilterBottomSheet extends StatelessWidget {
  const _FilterBottomSheet();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMD),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Filter & Sort',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingMD),
          
          // Sort Options
          const Text(
            'Sort By',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSizes.paddingSM),
          Column(
            children: [
              'Relevance',
              'Price: Low to High',
              'Price: High to Low',
              'Rating',
              'Newest',
            ].map((option) {
              return ListTile(
                title: Text(option),
                leading: Radio(
                  value: option,
                  groupValue: 'Relevance', // TODO: Implement actual selection
                  onChanged: (value) {
                    // TODO: Implement sort functionality
                  },
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: AppSizes.paddingMD),
          
          // Apply Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // TODO: Apply filters
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingMD),
              ),
              child: const Text(
                'Apply Filters',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class Timer {
  final Duration duration;
  final VoidCallback callback;
  
  Timer(this.duration, this.callback) {
    Future.delayed(duration, callback);
  }
  
  void cancel() {
    // Implementation for canceling timer
  }
}