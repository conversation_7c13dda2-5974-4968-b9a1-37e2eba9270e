import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../utils.dart';
import '../constants.dart';

class RememberEmailService {
  static final RememberEmailService _instance =
      RememberEmailService._internal();
  factory RememberEmailService() => _instance;
  RememberEmailService._internal();

  static const int _maxRememberedEmails = 5;

  Future<void> addRememberedEmail(String email) async {
    try {
      final emails = await getRememberedEmails();

      // Remove email if it already exists (to move it to the top)
      emails.removeWhere((e) => e.toLowerCase() == email.toLowerCase());

      // Add email to the beginning of the list
      emails.insert(0, email);

      // Keep only the most recent emails (max 5)
      if (emails.length > _maxRememberedEmails) {
        emails.removeRange(_maxRememberedEmails, emails.length);
      }

      await _saveRememberedEmails(emails);
    } catch (e) {
      // Log error but don't throw - this is not critical functionality
      debugPrint('Error adding remembered email: $e');
    }
  }

  Future<List<String>> getRememberedEmails() async {
    try {
      final jsonString = await AppUtils.getSecureData(AppConstants.rememberedEmailsKey);
      if (jsonString != null) {
        final List<dynamic> emailList = jsonDecode(jsonString);
        return emailList.cast<String>();
      }
    } catch (e) {
      debugPrint('Error getting remembered emails: $e');
    }
    return [];
  }

  Future<void> removeRememberedEmail(String email) async {
    try {
      final emails = await getRememberedEmails();
      emails.removeWhere((e) => e.toLowerCase() == email.toLowerCase());
      await _saveRememberedEmails(emails);
    } catch (e) {
      debugPrint('Error removing remembered email: $e');
    }
  }

  Future<void> clearAllRememberedEmails() async {
    try {
      await AppUtils.deleteSecureData(AppConstants.rememberedEmailsKey);
    } catch (e) {
      debugPrint('Error clearing all remembered emails: $e');
    }
  }

  Future<String?> getLastRememberedEmail() async {
    try {
      final emails = await getRememberedEmails();
      return emails.isNotEmpty ? emails.first : null;
    } catch (e) {
      return null;
    }
  }

  Future<bool> isEmailRemembered(String email) async {
    try {
      final emails = await getRememberedEmails();
      return emails.any((e) => e.toLowerCase() == email.toLowerCase());
    } catch (e) {
      return false;
    }
  }

  Future<void> _saveRememberedEmails(List<String> emails) async {
    try {
      final jsonString = jsonEncode(emails);
      await AppUtils.saveSecureData(AppConstants.rememberedEmailsKey, jsonString);
    } catch (e) {
      debugPrint('Error saving remembered emails: $e');
    }
  }
}
