class ResetPasswordRequest {
  final String username;
  final String password;

  ResetPasswordRequest({required this.username, required this.password});

  Map<String, dynamic> toJson() {
    return {'username': username, 'password': password};
  }
}

class ResetPasswordResponse {
  final bool error;
  final String message;
  final dynamic responseData;

  ResetPasswordResponse({
    required this.error,
    required this.message,
    this.responseData,
  });

  factory ResetPasswordResponse.fromJson(Map<String, dynamic> json) {
    return ResetPasswordResponse(
      error: json['error'] ?? true,
      message: json['message'] ?? '',
      responseData: json['responseData'],
    );
  }

  bool get success => !error && responseData != null;
}
