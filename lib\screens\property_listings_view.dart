import 'package:flutter/material.dart';

class PropertyListingsView extends StatelessWidget {
  final List<PropertyListing> listings;
  final void Function(PropertyListing) onListingTap;

  const PropertyListingsView({
    super.key,
    required this.listings,
    required this.onListingTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab bar for Listings/Sales/Leases
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.purpleAccent),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              _TabButton(label: 'Listings', selected: true),
              _TabButton(label: 'Sales', selected: false),
              _TabButton(label: 'Leases', selected: false),
            ],
          ),
        ),
        // Filter buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _FilterButton(label: 'For Lease', selected: true),
            _FilterButton(label: 'For Sale', selected: false),
            _FilterButton(label: 'Lease or Sale', selected: false),
          ],
        ),
        const SizedBox(height: 16),
        // Listings
        ...listings.map(
          (listing) => _ListingCard(
            listing: listing,
            onTap: () => onListingTap(listing),
          ),
        ),
      ],
    );
  }
}

class _TabButton extends StatelessWidget {
  final String label;
  final bool selected;
  const _TabButton({required this.label, required this.selected});
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: selected ? Colors.white : Colors.grey[200],
          border: Border(
            bottom: BorderSide(
              color: selected ? Colors.purpleAccent : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              fontWeight: selected ? FontWeight.bold : FontWeight.normal,
              color: selected ? Colors.black : Colors.grey,
            ),
          ),
        ),
      ),
    );
  }
}

class _FilterButton extends StatelessWidget {
  final String label;
  final bool selected;
  const _FilterButton({required this.label, required this.selected});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: selected ? Colors.black : Colors.white,
          foregroundColor: selected ? Colors.white : Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        onPressed: () {},
        child: Text(label),
      ),
    );
  }
}

class _ListingCard extends StatelessWidget {
  final PropertyListing listing;
  final VoidCallback onTap;
  const _ListingCard({required this.listing, required this.onTap});
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${listing.totalAvailable} SQM',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Total Available',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          listing.askingRate,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Asking Rate/SQM/Yr',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          listing.listingType,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Listing Type',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          listing.agentName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          listing.agentCompany,
                          style: const TextStyle(color: Colors.grey),
                        ),
                        Row(
                          children: [
                            Icon(Icons.phone, size: 18),
                            const SizedBox(width: 8),
                            Icon(Icons.email, size: 18),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${listing.minDiv} SQM',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Min Div',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          listing.rateType,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Rate Type',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          listing.agreementType,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Agreement Type',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          listing.agentName2,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          listing.agentCompany2,
                          style: const TextStyle(color: Colors.grey),
                        ),
                        Row(
                          children: [
                            Icon(Icons.phone, size: 18),
                            const SizedBox(width: 8),
                            Icon(Icons.email, size: 18),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                listing.description,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: onTap,
                  icon: const Icon(Icons.add_circle, size: 24),
                  label: const Text('More'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PropertyListing {
  final int totalAvailable;
  final int minDiv;
  final String askingRate;
  final String rateType;
  final String listingType;
  final String agreementType;
  final String agentName;
  final String agentCompany;
  final String agentName2;
  final String agentCompany2;
  final String description;

  PropertyListing({
    required this.totalAvailable,
    required this.minDiv,
    required this.askingRate,
    required this.rateType,
    required this.listingType,
    required this.agreementType,
    required this.agentName,
    required this.agentCompany,
    required this.agentName2,
    required this.agentCompany2,
    required this.description,
  });
}
