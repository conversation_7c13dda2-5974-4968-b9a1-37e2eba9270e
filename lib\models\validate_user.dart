import '../enums/auth_enums.dart';

/// Request model for username validation API
class ValidateUserRequest {
  final String username;

  ValidateUserRequest({required this.username});

  Map<String, dynamic> toJson() {
    return {'Username': username};
  }

  factory ValidateUserRequest.fromJson(Map<String, dynamic> json) {
    return ValidateUserRequest(username: json['Username'] ?? '');
  }
}

/// Response model for username validation API
class ValidateUserResponse {
  final bool error;
  final String message;
  final ValidateUserResponseData responseData;

  ValidateUserResponse({
    required this.error,
    required this.message,
    required this.responseData,
  });

  factory ValidateUserResponse.fromJson(Map<String, dynamic> json) {
    return ValidateUserResponse(
      error: json['error'] ?? true,
      message: json['message'] ?? '',
      responseData: ValidateUserResponseData.from<PERSON>son(
        json['responseData'] ?? {},
      ),
    );
  }

  /// Check if user is registered and can proceed to login
  bool get isRegistered =>
      responseData.loginStatus.isNotEmpty &&
      responseData.loginStatus.first.status == 1;

  /// Check if validation was successful (not error and user is registered)
  bool get success => !error && isRegistered;

  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'message': message,
      'responseData': responseData.toJson(),
    };
  }
}

/// Response data structure for username validation
class ValidateUserResponseData {
  final List<LoginStatus> loginStatus;
  final List<dynamic> userDetails;

  ValidateUserResponseData({
    required this.loginStatus,
    required this.userDetails,
  });

  factory ValidateUserResponseData.fromJson(Map<String, dynamic> json) {
    return ValidateUserResponseData(
      loginStatus: (json['LoginStatus'] as List<dynamic>? ?? [])
          .map((e) => LoginStatus.fromJson(e))
          .toList(),
      userDetails: json['UserDetails'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'LoginStatus': loginStatus.map((e) => e.toJson()).toList(),
      'UserDetails': userDetails,
    };
  }
}

/// Login status information from username validation
class LoginStatus {
  final int status;
  final int entityId;
  final int emailCount;
  final int forcefullyResetPassword;

  LoginStatus({
    required this.status,
    required this.entityId,
    required this.emailCount,
    required this.forcefullyResetPassword,
  });

  factory LoginStatus.fromJson(Map<String, dynamic> json) {
    return LoginStatus(
      status: json['Status'] ?? 0,
      entityId: json['EntityID'] ?? 0,
      emailCount: json['EmailCount'] ?? 0,
      forcefullyResetPassword: json['forcefullyresetpassword'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Status': status,
      'EntityID': entityId,
      'EmailCount': emailCount,
      'forcefullyresetpassword': forcefullyResetPassword,
    };
  }

  /// Check if user is registered (status = 1 means success)
  bool get isRegistered => status == LoginStatusCode.success.value;

  /// Check if user needs password reset
  bool get needsPasswordReset => forcefullyResetPassword == 1;

  /// Check if user has multiple email accounts
  bool get hasMultipleEmails => emailCount > 1;
}
