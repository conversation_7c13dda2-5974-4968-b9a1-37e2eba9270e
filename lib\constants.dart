import 'package:flutter/material.dart';

class AppConstants {
  static const String appName = 'Retail App';
  static const String appVersion = '1.0.0';

  // API URLs
  static const String devBaseUrl = 'https://pro-dev.arealytics.com.au/api/';
  static const String stagingBaseUrl = 'https://api-staging.arealytics.com.au/';
  static const String prodBaseUrl = 'https://api.arealytics.com.au/';

  // Current environment - change this based on build configuration
  static const Environment currentEnvironment = Environment.dev;

  static String get baseUrl {
    switch (currentEnvironment) {
      case Environment.dev:
        return devBaseUrl;
      case Environment.staging:
        return stagingBaseUrl;
      case Environment.prod:
        return prodBaseUrl;
    }
  }

  // API Endpoints
  static const String login = 'user/login';
  static const String validateUsername = 'login/validateUsername';
  static const String register = 'auth/register';
  static const String profile = 'user/profile';
  static const String propertyDetails = 'property/propertyDetails';

  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userIdKey = 'user_id';
  static const String userDataKey = 'user_data';
  static const String cartItemsKey = 'cart_items';
  static const String wishlistItemsKey = 'wishlist_items';
  static const String themePreferenceKey = 'theme_preference';
  static const String languagePreferenceKey = 'language_preference';
  static const String rememberedEmailsKey = 'remembered_emails';
  static const String apiTokenKey = 'api_token';
  static const String loginUserInfoKey = 'login_user_info';
  static const String visitorIdKey = 'visitor_id';

  // App Settings
  static const int apiTimeoutDuration = 30000; // 30 seconds
  static const int maxRetryAttempts = 3;
  static const String defaultCurrency = 'AUD';
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Image Settings
  static const int maxImageSizeInBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp',
  ];

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;

  // Error Messages
  static const String noInternetMessage =
      'No internet connection. Please check your network.';
  static const String serverErrorMessage =
      'Something went wrong. Please try again later.';
  static const String timeoutErrorMessage =
      'Request timeout. Please try again.';
  static const String unknownErrorMessage = 'An unknown error occurred.';
  static const String validationErrorMessage =
      'Please check your input and try again.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful!';
  static const String registerSuccessMessage = 'Account created successfully!';
  static const String logoutSuccessMessage = 'Logged out successfully!';
  static const String profileUpdateSuccessMessage =
      'Profile updated successfully!';
  static const String itemAddedToCartMessage = 'Item added to cart!';
  static const String itemRemovedFromCartMessage = 'Item removed from cart!';
  static const String itemAddedToWishlistMessage = 'Item added to wishlist!';
  static const String itemRemovedFromWishlistMessage =
      'Item removed from wishlist!';
}

/// String constants for the app
class AppStrings {
  // Login Screen
  static const String signInToArealytics = 'Sign in to your\nArealytics';
  static const String accountText = 'Account';
  static const String loginSubtitle = 'Enter your email and password to log in';
  static const String emailPlaceholder = '<EMAIL>';
  static const String passwordPlaceholder = '*****';
  static const String rememberMe = 'Remember me';
  static const String forgotPassword = 'Forgot Password ?';
  static const String continueButton = 'Continue';
  static const String loginButton = 'Log In';
  static const String backToLogin = 'Back to Login';

  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String validEmailRequired = 'Please enter a valid email address';
  static const String passwordRequired = 'Password is required';
  static const String confirmPasswordRequired = 'Confirm password is required';
  static const String passwordsDoNotMatch = 'Passwords do not match';
  static const String userNotFound = 'User not found. Please check your email address.';
  static const String userValidationFailed = 'User validation failed. Please check your email address.';
  static const String usernameValidationFailed = 'Failed to validate username';
  static const String usernameValidationRetry = 'Failed to validate username. Please try again.';
  static const String loginFailed = 'Login failed. Please try again.';

  // Forgot Password Screen
  static const String forgotPasswordTitle = 'Forgot Password';
  static const String resetPasswordTitle = 'Reset Your Password';
  static const String forgotPasswordSubtitle = 'Enter your email address and we\'ll send you a verification code to reset your password.';
  static const String sendVerificationCode = 'Send Verification Code';
  static const String verificationCodeSent = 'Verification code sent successfully!';
  static const String verificationCodeFailed = 'Failed to send verification code. Please try again.';

  // Verification Code Screen
  static const String verifyCodeTitle = 'Verify Code';
  static const String enterVerificationCode = 'Enter Verification Code';
  static const String verificationCodePlaceholder = 'Enter 4-6 digit code';
  static const String verifyCodeButton = 'Verify Code';
  static const String resendCode = 'Resend Code';
  static const String backToEmailEntry = 'Back to Email Entry';
  static const String codeVerified = 'Code verified successfully!';
  static const String codeVerificationFailed = 'Failed to verify code. Please try again.';
  static const String verificationCodeRequired = 'Verification code is required';
  static const String enterValidCode = 'Please enter a valid code';
  static const String verificationCodeSentAgain = 'Verification code sent again!';
  static const String resendCodeFailed = 'Failed to resend code. Please try again.';

  // Reset Password Screen
  static const String createNewPassword = 'Create New Password';
  static const String resetPasswordSubtitle = 'Enter your new password. Make sure it\'s strong and secure.';
  static const String newPasswordPlaceholder = 'New Password';
  static const String confirmPasswordPlaceholder = 'Confirm Password';
  static const String resetPasswordButton = 'Reset Password';
  static const String passwordResetSuccess = 'Password reset successfully!';
  static const String passwordResetFailed = 'Failed to reset password. Please try again.';
  static const String passwordRequirementsMissing = 'Password must meet all requirements';

  // Password Strength
  static const String passwordStrengthWeak = 'Weak';
  static const String passwordStrengthMedium = 'Medium';
  static const String passwordStrengthStrong = 'Strong';
  static const String passwordRequired8Chars = 'at least 8 characters';
  static const String passwordRequiredUppercase = 'one uppercase letter';
  static const String passwordRequiredLowercase = 'one lowercase letter';
  static const String passwordRequiredNumber = 'one number';
  static const String strongPassword = 'Strong password';

  // Common
  static const String loading = 'Loading...';
  static const String retry = 'Retry';
  static const String cancel = 'Cancel';
  static const String ok = 'OK';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String remove = 'Remove';
  static const String close = 'Close';
  static const String done = 'Done';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String skip = 'Skip';

  // Fonts
  static const String defaultFontFamily = 'Inter';
}

enum Environment { dev, staging, prod }

class ApiEndpoints {
  static String login() => '${AppConstants.baseUrl}${AppConstants.login}';
  static String validateUsername() =>
      '${AppConstants.baseUrl}${AppConstants.validateUsername}';
  static String register() => '${AppConstants.baseUrl}${AppConstants.register}';
  static String profile() => '${AppConstants.baseUrl}${AppConstants.profile}';
}

class AppColors {
  static const primaryColor = Color(0xFF2196F3);
  static const primaryDarkColor = Color(0xFF1976D2);
  static const primaryLightColor = Color(0xFFBBDEFB);
  static const accentColor = Color(0xFFFF5722);
  static const backgroundColor = Color(0xFFF5F5F5);
  static const surfaceColor = Color(0xFFFFFFFF);
  static const errorColor = Color(0xFFF44336);
  static const successColor = Color(0xFF4CAF50);
  static const warningColor = Color(0xFFFF9800);
  static const infoColor = Color(0xFF2196F3);

  // Login/Auth Colors
  static const loginPrimaryColor = Color(0xFF2567E8);
  static const loginPrimaryDarkColor = Color(0xFF1E5BC7);
  static const loginButtonColor = Color(0xFF6C63FF);
  static const loginAccentColor = Color(0xFFFFEB3B);

  // Form Colors
  static const inputBackgroundColor = Color(0xFFF5F5F5);
  static const inputHintColor = Color(0xFF9E9E9E);
  static const dropdownShadowColor = Color(0x4D000000); // Black with 30% opacity

  // Text Colors
  static const textPrimaryColor = Color(0xFF212121);
  static const textSecondaryColor = Color(0xFF757575);
  static const textHintColor = Color(0xFF9E9E9E);
  static const textLightColor = Color(0xFFFFFFFF);

  // Border Colors
  static const borderColor = Color(0xFFE0E0E0);
  static const dividerColor = Color(0xFFBDBDBD);

  // Password Strength Colors
  static const passwordWeakColor = Color(0xFFF44336);    // Red
  static const passwordMediumColor = Color(0xFFFF9800);  // Orange
  static const passwordStrongColor = Color(0xFF4CAF50);  // Green
}

class AppSizes {
  // Padding and Margins
  static const double paddingXS = 4.0;
  static const double paddingSM = 8.0;
  static const double paddingMD = 16.0;
  static const double paddingLG = 24.0;
  static const double paddingXL = 32.0;

  // Border Radius
  static const double radiusXS = 4.0;
  static const double radiusSM = 8.0;
  static const double radiusMD = 12.0;
  static const double radiusLG = 16.0;
  static const double radiusXL = 24.0;

  // Icon Sizes
  static const double iconXS = 16.0;
  static const double iconSM = 20.0;
  static const double iconMD = 24.0;
  static const double iconLG = 32.0;
  static const double iconXL = 48.0;

  // Button Heights
  static const double buttonHeightSM = 36.0;
  static const double buttonHeightMD = 48.0;
  static const double buttonHeightLG = 56.0;

  // App Bar
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 60.0;
}
