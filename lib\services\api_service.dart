import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../constants.dart';
import '../utils.dart';
import '../models/login_models.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late final Dio _dio;
  final String _baseUrl = AppConstants.baseUrl;

  void initialize() {
    _dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        connectTimeout: const Duration(
          milliseconds: AppConstants.apiTimeoutDuration,
        ),
        receiveTimeout: const Duration(
          milliseconds: AppConstants.apiTimeoutDuration,
        ),
        sendTimeout: const Duration(
          milliseconds: AppConstants.apiTimeoutDuration,
        ),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Referer': 'https://pro-dev.arealytics.com.au/',
          'Origin': 'https://pro-dev.arealytics.com.au',
          'User-Agent': 'RetailApp/1.0.0 (Flutter)',
        },
      ),
    );

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          debugPrint('REQUEST[${options.method}] => PATH: ${options.path}');
          debugPrint('Headers: ${options.headers}');
          debugPrint('Data: ${options.data}');

          // Add auth token from login response for API calls
          final token = await AppUtils.getApiToken();
          debugPrint('TOKEN ${token}');
          if (token != null && token.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $token';
            debugPrint('Added Authorization header with token');
          }

          // Ensure required headers are always present
          options.headers['Referer'] = 'https://pro-dev.arealytics.com.au/';
          options.headers['Origin'] = 'https://pro-dev.arealytics.com.au';
          options.headers['User-Agent'] = 'RetailApp/1.0.0 (Flutter)';

          handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint(
            'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
          );
          debugPrint('Data: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) async {
          debugPrint(
            'ERROR[${error.response?.statusCode}] => PATH: ${error.requestOptions.path}',
          );
          debugPrint('Error: ${error.message}');
          debugPrint('Response: ${error.response?.data}');

          // Handle token refresh on 401
          if (error.response?.statusCode == 401) {
            try {
              final refreshed = await _refreshToken();
              if (refreshed) {
                // Retry the original request
                final options = error.requestOptions;
                final token = await AppUtils.getApiToken();
                if (token != null) {
                  options.headers['Authorization'] = 'Bearer $token';
                }

                final retryResponse = await _dio.request(
                  options.path,
                  options: Options(
                    method: options.method,
                    headers: options.headers,
                  ),
                  data: options.data,
                  queryParameters: options.queryParameters,
                );

                return handler.resolve(retryResponse);
              } else {
                // Refresh failed, logout user
                await _handleLogout();
              }
            } catch (e) {
              debugPrint('Token refresh failed: $e');
              await _handleLogout();
            }
          }

          handler.next(error);
        },
      ),
    );

    // Add logging interceptor for debug builds
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: false,
        ),
      );
    }
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await AppUtils.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
        options: Options(
          headers: {'Authorization': null}, // Remove auth header for refresh
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['access_token'] != null) {
          await AppUtils.saveAccessToken(data['access_token']);
          if (data['refresh_token'] != null) {
            await AppUtils.saveRefreshToken(data['refresh_token']);
          }
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint('Refresh token error: $e');
      return false;
    }
  }

  Future<void> _handleLogout() async {
    await AppUtils.clearTokens();
    // Navigate to login screen
    // This should be handled by a global navigation service
  }

  // Generic API methods
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? parser,
  ) {
    if (response.statusCode! >= 200 && response.statusCode! < 300) {
      T? data;
      if (parser != null && response.data != null) {
        data = parser(response.data);
      } else {
        data = response.data;
      }

      // For business logic success, check the parsed data's success property
      bool isSuccess = true;
      String message = response.data['message'] ?? 'Success';

      if (data != null) {
        // Check if the parsed data has a success getter using dynamic casting
        try {
          final dynamic dynamicData = data;
          if (dynamicData is LoginResponse) {
            isSuccess = dynamicData.success;
            if (!isSuccess && dynamicData.errorMessage != null) {
              message = dynamicData.errorMessage!;
            }
          } else if (dynamicData is ValidateUsernameResponse) {
            isSuccess = dynamicData.success;
          }
        } catch (e) {
          // If type checking fails, keep original success state
          debugPrint('Type checking failed for response data: $e');
        }
      }

      return ApiResponse<T>(
        success: isSuccess,
        data: data,
        message: message,
        statusCode: response.statusCode!,
      );
    } else {
      return ApiResponse<T>(
        success: false,
        message: response.data?['message'] ?? 'Unknown error occurred',
        statusCode: response.statusCode!,
        error: response.data?['error'],
      );
    }
  }

  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiResponse<T>(
            success: false,
            message: AppConstants.timeoutErrorMessage,
            statusCode: 408,
            error: error.message,
          );

        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode ?? 500;
          final message =
              error.response?.data?['message'] ??
              _getStatusCodeMessage(statusCode);

          return ApiResponse<T>(
            success: false,
            message: message,
            statusCode: statusCode,
            error: error.response?.data?['error'],
          );

        case DioExceptionType.cancel:
          return ApiResponse<T>(
            success: false,
            message: 'Request was cancelled',
            statusCode: 499,
            error: error.message,
          );

        case DioExceptionType.connectionError:
          return ApiResponse<T>(
            success: false,
            message: AppConstants.noInternetMessage,
            statusCode: 503,
            error: error.message,
          );

        case DioExceptionType.badCertificate:
          return ApiResponse<T>(
            success: false,
            message: 'Certificate verification failed',
            statusCode: 495,
            error: error.message,
          );

        case DioExceptionType.unknown:
        default:
          return ApiResponse<T>(
            success: false,
            message: AppUtils.getErrorMessage(error),
            statusCode: 500,
            error: error.message,
          );
      }
    }

    return ApiResponse<T>(
      success: false,
      message: AppUtils.getErrorMessage(error),
      statusCode: 500,
      error: error.toString(),
    );
  }

  String _getStatusCodeMessage(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Unauthorized. Please login again.';
      case 403:
        return 'Forbidden. You don\'t have permission to access this resource.';
      case 404:
        return 'Resource not found.';
      case 409:
        return 'Conflict. The resource already exists.';
      case 422:
        return AppConstants.validationErrorMessage;
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return AppConstants.serverErrorMessage;
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again later.';
      default:
        return AppConstants.serverErrorMessage;
    }
  }

  // File upload method
  Future<ApiResponse<T>> uploadFile<T>(
    String path,
    String filePath, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalFields,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? parser,
  }) async {
    try {
      final formData = FormData.fromMap({
        fieldName: await MultipartFile.fromFile(filePath),
        if (additionalFields != null) ...additionalFields,
      });

      final response = await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String message;
  final int statusCode;
  final dynamic error;

  ApiResponse({
    required this.success,
    this.data,
    required this.message,
    required this.statusCode,
    this.error,
  });

  @override
  String toString() {
    return 'ApiResponse(success: $success, data: $data, message: $message, statusCode: $statusCode, error: $error)';
  }
}
